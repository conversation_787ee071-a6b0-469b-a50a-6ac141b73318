using BecaTracktion.Models;
using BecaTracktion.Services;
using Microsoft.AspNetCore.Mvc;

namespace BecaTracktion.Controllers
{
    /// <summary>
    /// Controller for receiving telemetry data from client applications
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class TelemetryController : ControllerBase
    {
        private readonly ITelemetryService _telemetryService;
        private readonly ILogger<TelemetryController> _logger;

        public TelemetryController(
            ITelemetryService telemetryService,
            ILogger<TelemetryController> logger)
        {
            _telemetryService = telemetryService;
            _logger = logger;
        }

        /// <summary>
        /// Track a custom event
        /// </summary>
        /// <param name="telemetryEvent">The event to track</param>
        /// <returns>Response indicating success or failure</returns>
        [HttpPost("event")]
        [ProducesResponseType(typeof(TelemetryResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(TelemetryResponse), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(TelemetryResponse), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<TelemetryResponse>> TrackEvent([FromBody] TelemetryEvent telemetryEvent)
        {
            try
            {
                if (string.IsNullOrEmpty(telemetryEvent.EventName))
                {
                    return BadRequest(new TelemetryResponse
                    {
                        Success = false,
                        Message = "EventName is required",
                        ProcessedAt = DateTime.UtcNow,
                        ErrorDetails = "The EventName field cannot be null or empty"
                    });
                }

                await _telemetryService.TrackEventAsync(telemetryEvent);

                _logger.LogInformation("Event tracked: {EventName}", telemetryEvent.EventName);

                return Ok(new TelemetryResponse
                {
                    Success = true,
                    Message = "Event tracked successfully",
                    ProcessedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking event: {EventName}", telemetryEvent.EventName);

                return StatusCode(StatusCodes.Status500InternalServerError, new TelemetryResponse
                {
                    Success = false,
                    Message = "Failed to track event",
                    ProcessedAt = DateTime.UtcNow,
                    ErrorDetails = ex.Message
                });
            }
        }

        /// <summary>
        /// Track an exception
        /// </summary>
        /// <param name="telemetryException">The exception to track</param>
        /// <returns>Response indicating success or failure</returns>
        [HttpPost("exception")]
        [ProducesResponseType(typeof(TelemetryResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(TelemetryResponse), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(TelemetryResponse), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<TelemetryResponse>> TrackException([FromBody] TelemetryException telemetryException)
        {
            try
            {
                if (string.IsNullOrEmpty(telemetryException.Message))
                {
                    return BadRequest(new TelemetryResponse
                    {
                        Success = false,
                        Message = "Exception message is required",
                        ProcessedAt = DateTime.UtcNow,
                        ErrorDetails = "The Message field cannot be null or empty"
                    });
                }

                await _telemetryService.TrackExceptionAsync(telemetryException);

                _logger.LogInformation("Exception tracked: {Message}", telemetryException.Message);

                return Ok(new TelemetryResponse
                {
                    Success = true,
                    Message = "Exception tracked successfully",
                    ProcessedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking exception: {Message}", telemetryException.Message);

                return StatusCode(StatusCodes.Status500InternalServerError, new TelemetryResponse
                {
                    Success = false,
                    Message = "Failed to track exception",
                    ProcessedAt = DateTime.UtcNow,
                    ErrorDetails = ex.Message
                });
            }
        }

        /// <summary>
        /// Track a metric
        /// </summary>
        /// <param name="telemetryMetric">The metric to track</param>
        /// <returns>Response indicating success or failure</returns>
        [HttpPost("metric")]
        [ProducesResponseType(typeof(TelemetryResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(TelemetryResponse), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(TelemetryResponse), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<TelemetryResponse>> TrackMetric([FromBody] TelemetryMetric telemetryMetric)
        {
            try
            {
                if (string.IsNullOrEmpty(telemetryMetric.MetricName))
                {
                    return BadRequest(new TelemetryResponse
                    {
                        Success = false,
                        Message = "MetricName is required",
                        ProcessedAt = DateTime.UtcNow,
                        ErrorDetails = "The MetricName field cannot be null or empty"
                    });
                }

                await _telemetryService.TrackMetricAsync(telemetryMetric);

                _logger.LogInformation("Metric tracked: {MetricName} = {Value}", telemetryMetric.MetricName, telemetryMetric.Value);

                return Ok(new TelemetryResponse
                {
                    Success = true,
                    Message = "Metric tracked successfully",
                    ProcessedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking metric: {MetricName}", telemetryMetric.MetricName);

                return StatusCode(StatusCodes.Status500InternalServerError, new TelemetryResponse
                {
                    Success = false,
                    Message = "Failed to track metric",
                    ProcessedAt = DateTime.UtcNow,
                    ErrorDetails = ex.Message
                });
            }
        }

        /// <summary>
        /// Track a page view
        /// </summary>
        /// <param name="telemetryPageView">The page view to track</param>
        /// <returns>Response indicating success or failure</returns>
        [HttpPost("pageview")]
        [ProducesResponseType(typeof(TelemetryResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(TelemetryResponse), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(TelemetryResponse), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<TelemetryResponse>> TrackPageView([FromBody] TelemetryPageView telemetryPageView)
        {
            try
            {
                if (string.IsNullOrEmpty(telemetryPageView.PageName))
                {
                    return BadRequest(new TelemetryResponse
                    {
                        Success = false,
                        Message = "PageName is required",
                        ProcessedAt = DateTime.UtcNow,
                        ErrorDetails = "The PageName field cannot be null or empty"
                    });
                }

                await _telemetryService.TrackPageViewAsync(telemetryPageView);

                _logger.LogInformation("Page view tracked: {PageName}", telemetryPageView.PageName);

                return Ok(new TelemetryResponse
                {
                    Success = true,
                    Message = "Page view tracked successfully",
                    ProcessedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking page view: {PageName}", telemetryPageView.PageName);

                return StatusCode(StatusCodes.Status500InternalServerError, new TelemetryResponse
                {
                    Success = false,
                    Message = "Failed to track page view",
                    ProcessedAt = DateTime.UtcNow,
                    ErrorDetails = ex.Message
                });
            }
        }

        /// <summary>
        /// Health check endpoint
        /// </summary>
        /// <returns>Service health status</returns>
        [HttpGet("health")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public IActionResult Health()
        {
            return Ok(new
            {
                Status = "Healthy",
                Service = "Telemetry Microservice",
                Timestamp = DateTime.UtcNow
            });
        }

        /// <summary>
        /// Flush telemetry data to ensure it's sent to Application Insights
        /// </summary>
        /// <returns>Response indicating success or failure</returns>
        [HttpPost("flush")]
        [ProducesResponseType(typeof(TelemetryResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(TelemetryResponse), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<TelemetryResponse>> Flush()
        {
            try
            {
                await _telemetryService.FlushAsync();

                _logger.LogInformation("Telemetry flushed");

                return Ok(new TelemetryResponse
                {
                    Success = true,
                    Message = "Telemetry flushed successfully",
                    ProcessedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error flushing telemetry");

                return StatusCode(StatusCodes.Status500InternalServerError, new TelemetryResponse
                {
                    Success = false,
                    Message = "Failed to flush telemetry",
                    ProcessedAt = DateTime.UtcNow,
                    ErrorDetails = ex.Message
                });
            }
        }
    }
}

