# BecaTracktion Telemetry Microservice - Testing Guide

This guide provides instructions for testing the BecaTracktion Telemetry Microservice.

## Table of Contents

1. [Manual Testing](#manual-testing)
2. [Automated Testing](#automated-testing)
3. [Integration Testing](#integration-testing)
4. [Load Testing](#load-testing)
5. [Client Testing](#client-testing)

## Manual Testing

### Using Swagger UI

1. Start the application:
   ```bash
   dotnet run --project BecaTracktion
   ```

2. Open browser to: http://localhost:5004

3. Test each endpoint using the interactive UI

### Using cURL

#### Test Health Endpoint

```bash
curl -X GET http://localhost:5004/api/telemetry/health
```

Expected response:
```json
{
  "status": "Healthy",
  "service": "Telemetry Microservice",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### Test Event Tracking

```bash
curl -X POST http://localhost:5004/api/telemetry/event \
  -H "Content-Type: application/json" \
  -d '{
    "eventName": "Test Event",
    "properties": {
      "test": "value"
    },
    "userId": "test-user",
    "sessionId": "test-session"
  }'
```

Expected response:
```json
{
  "success": true,
  "message": "Event tracked successfully",
  "processedAt": "2024-01-15T10:30:00Z"
}
```

#### Test Exception Tracking

```bash
curl -X POST http://localhost:5004/api/telemetry/exception \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Test exception",
    "exceptionType": "TestException",
    "stackTrace": "at Test.Method()",
    "location": "Test.Method",
    "userId": "test-user"
  }'
```

#### Test Metric Tracking

```bash
curl -X POST http://localhost:5004/api/telemetry/metric \
  -H "Content-Type: application/json" \
  -d '{
    "metricName": "Test Metric",
    "value": 42.5,
    "userId": "test-user"
  }'
```

#### Test Page View Tracking

```bash
curl -X POST http://localhost:5004/api/telemetry/pageview \
  -H "Content-Type: application/json" \
  -d '{
    "pageName": "Test Page",
    "properties": {
      "url": "/test"
    },
    "userId": "test-user"
  }'
```

#### Test Flush

```bash
curl -X POST http://localhost:5004/api/telemetry/flush
```

### Using Postman

1. Import the Swagger definition from http://localhost:5004/swagger/v1/swagger.json
2. Create a new collection
3. Add requests for each endpoint
4. Save example requests and responses

## Automated Testing

### Unit Tests

Create a test project:

```bash
dotnet new xunit -n BecaTracktion.Tests
cd BecaTracktion.Tests
dotnet add reference ../BecaTracktion/BecaTracktion.csproj
dotnet add package Moq
dotnet add package Microsoft.AspNetCore.Mvc.Testing
```

Example unit test for TelemetryController:

```csharp
using Xunit;
using Moq;
using BecaTracktion.Controllers;
using BecaTracktion.Services;
using BecaTracktion.Models;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Mvc;

namespace BecaTracktion.Tests
{
    public class TelemetryControllerTests
    {
        private readonly Mock<ITelemetryService> _mockTelemetryService;
        private readonly Mock<ILogger<TelemetryController>> _mockLogger;
        private readonly TelemetryController _controller;

        public TelemetryControllerTests()
        {
            _mockTelemetryService = new Mock<ITelemetryService>();
            _mockLogger = new Mock<ILogger<TelemetryController>>();
            _controller = new TelemetryController(_mockTelemetryService.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task TrackEvent_ValidEvent_ReturnsOk()
        {
            // Arrange
            var telemetryEvent = new TelemetryEvent
            {
                EventName = "Test Event",
                Properties = new Dictionary<string, string> { { "key", "value" } }
            };

            // Act
            var result = await _controller.TrackEvent(telemetryEvent);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var response = Assert.IsType<TelemetryResponse>(okResult.Value);
            Assert.True(response.Success);
            Assert.Equal("Event tracked successfully", response.Message);
        }

        [Fact]
        public async Task TrackEvent_EmptyEventName_ReturnsBadRequest()
        {
            // Arrange
            var telemetryEvent = new TelemetryEvent
            {
                EventName = "",
                Properties = new Dictionary<string, string>()
            };

            // Act
            var result = await _controller.TrackEvent(telemetryEvent);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
            var response = Assert.IsType<TelemetryResponse>(badRequestResult.Value);
            Assert.False(response.Success);
        }

        [Fact]
        public async Task Health_ReturnsOk()
        {
            // Act
            var result = _controller.Health();

            // Assert
            Assert.IsType<OkObjectResult>(result);
        }
    }
}
```

Run tests:

```bash
dotnet test
```

## Integration Testing

### Test with Real Application Insights

1. Configure a test Application Insights resource
2. Update connection string in appsettings.Development.json
3. Run the application
4. Send test telemetry
5. Verify in Azure Portal:
   - Go to Application Insights resource
   - Navigate to Logs
   - Query for test data:

```kusto
// Query for custom events
customEvents
| where timestamp > ago(1h)
| where name == "Test Event"
| project timestamp, name, customDimensions

// Query for exceptions
exceptions
| where timestamp > ago(1h)
| where outerMessage contains "Test"
| project timestamp, outerMessage, details

// Query for custom metrics
customMetrics
| where timestamp > ago(1h)
| where name == "Test Metric"
| project timestamp, name, value
```

### End-to-End Test Script

Create `test-e2e.sh`:

```bash
#!/bin/bash

BASE_URL="http://localhost:5004"
SESSION_ID=$(uuidgen)

echo "Starting End-to-End Tests..."
echo "Session ID: $SESSION_ID"
echo ""

# Test 1: Health Check
echo "Test 1: Health Check"
response=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL/api/telemetry/health")
http_code=$(echo "$response" | tail -n1)
if [ "$http_code" -eq 200 ]; then
    echo "✓ PASSED"
else
    echo "✗ FAILED (HTTP $http_code)"
fi
echo ""

# Test 2: Track Event
echo "Test 2: Track Event"
response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/api/telemetry/event" \
  -H "Content-Type: application/json" \
  -d "{\"eventName\":\"E2E Test Event\",\"sessionId\":\"$SESSION_ID\"}")
http_code=$(echo "$response" | tail -n1)
if [ "$http_code" -eq 200 ]; then
    echo "✓ PASSED"
else
    echo "✗ FAILED (HTTP $http_code)"
fi
echo ""

# Test 3: Track Exception
echo "Test 3: Track Exception"
response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/api/telemetry/exception" \
  -H "Content-Type: application/json" \
  -d "{\"message\":\"E2E Test Exception\",\"sessionId\":\"$SESSION_ID\"}")
http_code=$(echo "$response" | tail -n1)
if [ "$http_code" -eq 200 ]; then
    echo "✓ PASSED"
else
    echo "✗ FAILED (HTTP $http_code)"
fi
echo ""

# Test 4: Track Metric
echo "Test 4: Track Metric"
response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/api/telemetry/metric" \
  -H "Content-Type: application/json" \
  -d "{\"metricName\":\"E2E Test Metric\",\"value\":99.9,\"sessionId\":\"$SESSION_ID\"}")
http_code=$(echo "$response" | tail -n1)
if [ "$http_code" -eq 200 ]; then
    echo "✓ PASSED"
else
    echo "✗ FAILED (HTTP $http_code)"
fi
echo ""

# Test 5: Track Page View
echo "Test 5: Track Page View"
response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/api/telemetry/pageview" \
  -H "Content-Type: application/json" \
  -d "{\"pageName\":\"E2E Test Page\",\"sessionId\":\"$SESSION_ID\"}")
http_code=$(echo "$response" | tail -n1)
if [ "$http_code" -eq 200 ]; then
    echo "✓ PASSED"
else
    echo "✗ FAILED (HTTP $http_code)"
fi
echo ""

# Test 6: Flush
echo "Test 6: Flush Telemetry"
response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/api/telemetry/flush")
http_code=$(echo "$response" | tail -n1)
if [ "$http_code" -eq 200 ]; then
    echo "✓ PASSED"
else
    echo "✗ FAILED (HTTP $http_code)"
fi
echo ""

echo "End-to-End Tests Complete"
echo "Check Application Insights for session: $SESSION_ID"
```

Run the test:

```bash
chmod +x test-e2e.sh
./test-e2e.sh
```

## Load Testing

### Using Apache Bench

```bash
# Test event endpoint with 1000 requests, 10 concurrent
ab -n 1000 -c 10 -p event.json -T application/json \
  http://localhost:5004/api/telemetry/event
```

Create `event.json`:
```json
{
  "eventName": "Load Test Event",
  "properties": {
    "test": "load"
  }
}
```

### Using k6

Install k6: https://k6.io/docs/getting-started/installation/

Create `load-test.js`:

```javascript
import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  stages: [
    { duration: '30s', target: 20 },
    { duration: '1m', target: 50 },
    { duration: '30s', target: 0 },
  ],
};

export default function () {
  const url = 'http://localhost:5004/api/telemetry/event';
  const payload = JSON.stringify({
    eventName: 'Load Test Event',
    properties: {
      test: 'load',
      timestamp: new Date().toISOString()
    },
    userId: `user-${__VU}`,
    sessionId: `session-${__ITER}`
  });

  const params = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  let response = http.post(url, payload, params);
  
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
  });

  sleep(1);
}
```

Run the test:

```bash
k6 run load-test.js
```

## Client Testing

### Test C# Client

```csharp
using BecaTelemetryClient;

var telemetry = new TelemetryClient("http://localhost:5004");

// Test event tracking
var success = await telemetry.TrackEventAsync(
    "Client Test Event",
    new Dictionary<string, string> { { "client", "csharp" } }
);

Console.WriteLine($"Event tracked: {success}");

// Test exception tracking
try
{
    throw new Exception("Test exception");
}
catch (Exception ex)
{
    success = await telemetry.TrackExceptionAsync(ex, "Test");
    Console.WriteLine($"Exception tracked: {success}");
}

// Flush
await telemetry.FlushAsync();
```

### Test Python Client

```python
from telemetry_client import TelemetryClient

telemetry = TelemetryClient('http://localhost:5004')

# Test event tracking
success = telemetry.track_event(
    'Client Test Event',
    properties={'client': 'python'}
)
print(f'Event tracked: {success}')

# Test exception tracking
try:
    raise Exception('Test exception')
except Exception as ex:
    success = telemetry.track_exception(ex, 'Test')
    print(f'Exception tracked: {success}')

# Flush
telemetry.flush()
```

### Test JavaScript Client

```javascript
const TelemetryClient = require('./telemetry-client');

const telemetry = new TelemetryClient('http://localhost:5004');

async function test() {
    // Test event tracking
    let success = await telemetry.trackEvent(
        'Client Test Event',
        { client: 'javascript' }
    );
    console.log(`Event tracked: ${success}`);

    // Test exception tracking
    try {
        throw new Error('Test exception');
    } catch (error) {
        success = await telemetry.trackException(error, 'Test');
        console.log(`Exception tracked: ${success}`);
    }

    // Flush
    await telemetry.flush();
}

test();
```

## Verification

After running tests, verify in Application Insights:

1. Go to Azure Portal
2. Navigate to your Application Insights resource
3. Go to Logs
4. Run queries to verify data:

```kusto
// All telemetry from last hour
union customEvents, exceptions, customMetrics, pageViews
| where timestamp > ago(1h)
| project timestamp, itemType, name
| order by timestamp desc
```

## Continuous Integration

Add to your CI/CD pipeline:

```yaml
# Example GitHub Actions workflow
name: Test

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup .NET
        uses: actions/setup-dotnet@v1
        with:
          dotnet-version: 8.0.x
      - name: Restore dependencies
        run: dotnet restore
      - name: Build
        run: dotnet build --no-restore
      - name: Test
        run: dotnet test --no-build --verbosity normal
```

## Best Practices

1. **Test in isolation**: Use mock services for unit tests
2. **Test integration**: Verify with real Application Insights in staging
3. **Load test**: Ensure service can handle expected load
4. **Monitor**: Check Application Insights during and after tests
5. **Automate**: Include tests in CI/CD pipeline
6. **Document**: Keep test cases and results documented

