# C# Telemetry Clients for Revit

This directory contains **synchronous telemetry clients** specifically designed for Revit add-ins.

---

## 🎯 Which Client Should I Use?

| Revit Version | .NET Target | Client File | JSON Library | Guide |
|---------------|-------------|-------------|--------------|-------|
| **2024, 2025, 2026+** | .NET 8 | `TelemetryClientSync.cs` | System.Text.Json | [REVIT_INTEGRATION_GUIDE.md](REVIT_INTEGRATION_GUIDE.md) |
| **2023 and below** | .NET Framework 4.8 | `TelemetryClientSyncNet48.cs` | Newtonsoft.Json | [NET_FRAMEWORK_GUIDE.md](NET_FRAMEWORK_GUIDE.md) |

---

## 🚀 Quick Start

### For Revit 2024+ (.NET 8)

1. **Copy file**: `TelemetryClientSync.cs` to your project
2. **No additional packages** needed (uses built-in System.Text.Json)
3. **Follow guide**: [REVIT_INTEGRATION_GUIDE.md](REVIT_INTEGRATION_GUIDE.md)

```csharp
using (var telemetry = new TelemetryClientSync("https://tracktion.azurewebsites.net"))
{
    telemetry.TrackEvent("Command Started");
    // Your logic
    telemetry.TrackEvent("Command Completed");
    telemetry.Flush();
}
```

### For Revit 2023 and below (.NET Framework 4.8)

1. **Copy file**: `TelemetryClientSyncNet48.cs` to your project
2. **Add NuGet package**: `Newtonsoft.Json` version 13.0.3
3. **Follow guide**: [NET_FRAMEWORK_GUIDE.md](NET_FRAMEWORK_GUIDE.md)

```csharp
using (var telemetry = new TelemetryClientSyncNet48("https://tracktion.azurewebsites.net"))
{
    telemetry.TrackEvent("Command Started");
    // Your logic
    telemetry.TrackEvent("Command Completed");
    telemetry.Flush();
}
```

---

## 📁 Files in This Directory

### Client Files
- **`TelemetryClient.cs`** - Async client for general .NET applications
- **`TelemetryClientSync.cs`** - Synchronous client for Revit 2024+ (.NET 8)
- **`TelemetryClientSyncNet48.cs`** - Synchronous client for Revit 2023- (.NET Framework 4.8)

### Documentation
- **`REVIT_INTEGRATION_GUIDE.md`** - Complete guide for Revit 2024+ integration
- **`NET_FRAMEWORK_GUIDE.md`** - Complete guide for Revit 2023 and below
- **`README.md`** - This file

---

## 🔧 Key Features

### Both Synchronous Clients Support:
- ✅ **TrackEvent()** - Custom events
- ✅ **TrackException()** - Exception tracking
- ✅ **TrackMetric()** - Performance metrics
- ✅ **TrackPageView()** - Page/screen views
- ✅ **Flush()** - Ensure data is sent
- ✅ **BecaBaseCommand integration** helpers
- ✅ **Same API** - easy to switch between versions

### Differences:
| Feature | .NET 8 Version | .NET Framework 4.8 Version |
|---------|----------------|----------------------------|
| **JSON Library** | System.Text.Json (built-in) | Newtonsoft.Json (NuGet) |
| **String Handling** | Modern C# syntax | Compatible syntax |
| **Performance** | Slightly faster | Slightly slower |
| **Compatibility** | Revit 2024+ | Revit 2023 and below |

---

## 🏗️ Integration Patterns

### Pattern 1: Simple Command
```csharp
[Transaction(TransactionMode.Manual)]
public class MyCommand : IExternalCommand
{
    public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
    {
        // Choose the right client for your Revit version
        using (var telemetry = new TelemetryClientSync("https://tracktion.azurewebsites.net"))
        // OR: using (var telemetry = new TelemetryClientSyncNet48("https://tracktion.azurewebsites.net"))
        {
            telemetry.TrackEvent("Command Started");
            
            try
            {
                // Your command logic
                DoWork();
                
                telemetry.TrackEvent("Command Completed");
                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                telemetry.TrackException(ex, "Execute");
                return Result.Failed;
            }
            finally
            {
                telemetry.Flush();
            }
        }
    }
}
```

### Pattern 2: BecaBaseCommand Integration
```csharp
// For .NET 8 (Revit 2024+)
var helper = new BecaBaseCommandWithTelemetry();

// For .NET Framework 4.8 (Revit 2023-)
var helper = new BecaBaseCommandWithTelemetryNet48();

helper.InitializeTelemetry(addinName, commandSubName, "https://tracktion.azurewebsites.net");
helper.ProcessingStarter(properties);
// Your logic
helper.ProcessingCompleted();
helper.Dispose();
```

---

## 🚨 Common Issues and Solutions

### Issue: "Could not load System.Text.Json" (Revit 2023-)
**Solution**: Use `TelemetryClientSyncNet48.cs` instead of `TelemetryClientSync.cs`

### Issue: "Could not load Newtonsoft.Json" (Revit 2024+)
**Solution**: Use `TelemetryClientSync.cs` instead of `TelemetryClientSyncNet48.cs`

### Issue: "Async methods not supported"
**Solution**: Both clients are fully synchronous - no async/await used

### Issue: "Telemetry not appearing in Application Insights"
**Solution**: 
1. Check your service URL is correct: `https://tracktion.azurewebsites.net`
2. Make sure you call `Flush()` before command ends
3. Check network connectivity

---

## 🧪 Testing

### Quick Test Method
```csharp
public void TestTelemetry()
{
    // Choose the right client for your Revit version
    using (var telemetry = new TelemetryClientSync("https://tracktion.azurewebsites.net"))
    // OR: using (var telemetry = new TelemetryClientSyncNet48("https://tracktion.azurewebsites.net"))
    {
        var success = telemetry.TrackEvent("Test Event", new Dictionary<string, string>
        {
            { "Test", "Value" },
            { "Revit Version", Application.VersionNumber }
        });

        if (success)
        {
            TaskDialog.Show("Success", "Telemetry sent successfully!");
        }
        else
        {
            TaskDialog.Show("Error", "Failed to send telemetry.");
        }

        telemetry.Flush();
    }
}
```

---

## 📊 Migration from Original TelemetryHandler

### Before (Original TelemetryHandler)
```csharp
// Old code with Application Insights SDK conflicts
var telemetry = TelemetryHandler.CreateTelemetryHandler(addinName, commandSubName, doc);
telemetry?.ProcessingStarter(doc);
// ... command logic ...
telemetry?.ProcessingCompleted();
```

### After (New HTTP-based approach)
```csharp
// New code - no SDK conflicts, works with all Revit versions
var helper = new BecaBaseCommandWithTelemetry(); // or BecaBaseCommandWithTelemetryNet48
helper.InitializeTelemetry(addinName, commandSubName, "https://tracktion.azurewebsites.net");
helper.ProcessingStarter(properties);
// ... command logic ...
helper.ProcessingCompleted();
helper.Dispose();
```

### Benefits of Migration
- ✅ **No SDK conflicts** - works with all Revit versions
- ✅ **No conditional compilation** - same code for all versions
- ✅ **Centralized telemetry** - all data goes to one place
- ✅ **Better reliability** - HTTP is more stable than SDK
- ✅ **Easier maintenance** - update service, not all add-ins

---

## 🎯 Next Steps

1. **Identify your Revit versions**
2. **Choose the appropriate client**
3. **Follow the relevant guide**:
   - Revit 2024+ → [REVIT_INTEGRATION_GUIDE.md](REVIT_INTEGRATION_GUIDE.md)
   - Revit 2023- → [NET_FRAMEWORK_GUIDE.md](NET_FRAMEWORK_GUIDE.md)
4. **Test with a simple command**
5. **Integrate with your existing BecaBaseCommand**
6. **Deploy to all your add-ins**

---

## 📞 Support

- **Documentation**: Read the relevant guide for your Revit version
- **Testing**: Use the health check endpoint to verify connectivity
- **Troubleshooting**: Check the troubleshooting sections in the guides
- **Service Status**: Visit `https://tracktion.azurewebsites.net/api/telemetry/health`

---

**You're all set to integrate telemetry with any Revit version!** 🚀
