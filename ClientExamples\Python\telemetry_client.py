"""
BecaTracktion Telemetry Client for Python
Can be used in Python scripts, Dynamo scripts, or any Python application
"""

import requests
import json
from datetime import datetime
from typing import Dict, Optional
import uuid
import os


class TelemetryClient:
    """Client for sending telemetry to BecaTracktion microservice"""
    
    def __init__(self, base_url: str, user_id: Optional[str] = None):
        """
        Initialize the telemetry client
        
        Args:
            base_url: Base URL of the telemetry service (e.g., 'http://localhost:5004')
            user_id: Optional user identifier (defaults to OS username)
        """
        self.base_url = base_url.rstrip('/')
        self.user_id = user_id or os.getenv('USERNAME') or os.getenv('USER') or 'unknown'
        self.session_id = str(uuid.uuid4())
        self.timeout = 10
    
    def track_event(
        self,
        event_name: str,
        properties: Optional[Dict[str, str]] = None,
        metrics: Optional[Dict[str, float]] = None
    ) -> bool:
        """
        Track a custom event
        
        Args:
            event_name: Name of the event
            properties: Optional dictionary of string properties
            metrics: Optional dictionary of numeric metrics
            
        Returns:
            True if successful, False otherwise
        """
        try:
            payload = {
                'eventName': event_name,
                'properties': properties or {},
                'metrics': metrics,
                'userId': self.user_id,
                'sessionId': self.session_id,
                'timestamp': datetime.utcnow().isoformat() + 'Z'
            }
            
            response = requests.post(
                f'{self.base_url}/api/telemetry/event',
                json=payload,
                timeout=self.timeout
            )
            
            return response.status_code == 200
        except Exception as e:
            print(f"Error tracking event: {e}")
            return False
    
    def track_exception(
        self,
        exception: Exception,
        location: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None
    ) -> bool:
        """
        Track an exception
        
        Args:
            exception: The exception to track
            location: Optional location where the exception occurred
            properties: Optional dictionary of additional properties
            
        Returns:
            True if successful, False otherwise
        """
        try:
            import traceback
            
            payload = {
                'message': str(exception),
                'exceptionType': type(exception).__name__,
                'stackTrace': traceback.format_exc(),
                'location': location,
                'properties': properties or {},
                'userId': self.user_id,
                'sessionId': self.session_id,
                'timestamp': datetime.utcnow().isoformat() + 'Z'
            }
            
            response = requests.post(
                f'{self.base_url}/api/telemetry/exception',
                json=payload,
                timeout=self.timeout
            )
            
            return response.status_code == 200
        except Exception as e:
            print(f"Error tracking exception: {e}")
            return False
    
    def track_metric(
        self,
        metric_name: str,
        value: float,
        properties: Optional[Dict[str, str]] = None
    ) -> bool:
        """
        Track a metric
        
        Args:
            metric_name: Name of the metric
            value: Numeric value of the metric
            properties: Optional dictionary of additional properties
            
        Returns:
            True if successful, False otherwise
        """
        try:
            payload = {
                'metricName': metric_name,
                'value': value,
                'properties': properties,
                'userId': self.user_id,
                'sessionId': self.session_id,
                'timestamp': datetime.utcnow().isoformat() + 'Z'
            }
            
            response = requests.post(
                f'{self.base_url}/api/telemetry/metric',
                json=payload,
                timeout=self.timeout
            )
            
            return response.status_code == 200
        except Exception as e:
            print(f"Error tracking metric: {e}")
            return False
    
    def track_page_view(
        self,
        page_name: str,
        properties: Optional[Dict[str, str]] = None
    ) -> bool:
        """
        Track a page view
        
        Args:
            page_name: Name of the page/view
            properties: Optional dictionary of additional properties
            
        Returns:
            True if successful, False otherwise
        """
        try:
            payload = {
                'pageName': page_name,
                'properties': properties,
                'userId': self.user_id,
                'sessionId': self.session_id,
                'timestamp': datetime.utcnow().isoformat() + 'Z'
            }
            
            response = requests.post(
                f'{self.base_url}/api/telemetry/pageview',
                json=payload,
                timeout=self.timeout
            )
            
            return response.status_code == 200
        except Exception as e:
            print(f"Error tracking page view: {e}")
            return False
    
    def flush(self) -> bool:
        """
        Flush telemetry to ensure it's sent
        
        Returns:
            True if successful, False otherwise
        """
        try:
            response = requests.post(
                f'{self.base_url}/api/telemetry/flush',
                timeout=self.timeout
            )
            
            return response.status_code == 200
        except Exception as e:
            print(f"Error flushing telemetry: {e}")
            return False


# Example usage
def example_usage():
    """Example of using the telemetry client in a Python script"""
    
    telemetry = TelemetryClient('http://localhost:5004')
    
    # Track script start
    telemetry.track_event(
        'Python Script Started',
        properties={
            'script_name': 'example_script.py',
            'python_version': '3.11'
        }
    )
    
    try:
        # Your script logic here
        import time
        start_time = time.time()
        
        # Simulate some work
        time.sleep(1)
        
        # Track a metric
        duration = time.time() - start_time
        telemetry.track_metric('Script Duration', duration)
        
        # Track success
        telemetry.track_event(
            'Python Script Completed',
            properties={
                'status': 'success',
                'duration': f'{duration:.2f}'
            }
        )
        
    except Exception as ex:
        # Track exception
        telemetry.track_exception(
            ex,
            location='example_usage',
            properties={'script_name': 'example_script.py'}
        )
        
        telemetry.track_event(
            'Python Script Failed',
            properties={'error': str(ex)}
        )
    
    finally:
        # Ensure telemetry is sent
        telemetry.flush()


if __name__ == '__main__':
    example_usage()

