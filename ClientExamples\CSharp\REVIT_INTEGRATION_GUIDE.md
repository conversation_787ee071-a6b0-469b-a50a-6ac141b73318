# Revit Add-in Integration Guide - Synchronous Telemetry Client

This guide shows how to integrate the **synchronous** TelemetryClient with your Revit add-ins. The synchronous version is specifically designed for Revit, which doesn't work well with async/await.

## 🎯 Which Client to Use?

**Important**: Choose the correct client based on your Revit version!

| Revit Version | .NET Target | Client File | Guide |
|---------------|-------------|-------------|-------|
| **2024-2026+** | .NET 8 | `TelemetryClientSync.cs` | This guide ✅ |
| **2023 and below** | .NET Framework 4.8 | `TelemetryClientSyncNet48.cs` | [NET_FRAMEWORK_GUIDE.md](NET_FRAMEWORK_GUIDE.md) |

> **For Revit 2023 and below**: Use [NET_FRAMEWORK_GUIDE.md](NET_FRAMEWORK_GUIDE.md) instead of this guide!

## Why Synchronous?

Revit's `IExternalCommand.Execute` method is synchronous and doesn't support async/await properly. Using async code in Revit can cause:
- Deadlocks
- UI freezing
- Unpredictable behavior
- Context switching issues

The synchronous clients solve this by using synchronous HTTP calls.

## Quick Start

### Step 1: Add the Client to Your Project

1. Copy `TelemetryClientSync.cs` to your Revit add-in project
2. Make sure you have these NuGet packages:
   - `System.Net.Http` (usually included)
   - `System.Text.Json` (for .NET Framework 4.7.2+) or `Newtonsoft.Json` (for older versions)

### Step 2: Basic Usage in Revit Command

```csharp
using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaTelemetryClient;
using System;
using System.Collections.Generic;

namespace MyRevitAddin
{
    [Transaction(TransactionMode.Manual)]
    public class MyCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            // Initialize telemetry client
            using (var telemetry = new TelemetryClientSync("http://your-telemetry-service:8080"))
            {
                UIApplication uiapp = commandData.Application;
                UIDocument uidoc = uiapp.ActiveUIDocument;
                Document doc = uidoc.Document;

                // Track command start
                telemetry.TrackEvent(
                    "My Command Started",
                    new Dictionary<string, string>
                    {
                        { "Revit Version", uiapp.Application.VersionNumber },
                        { "Project Number", doc.ProjectInformation.Number },
                        { "Model Name", doc.Title },
                        { "User Name", Environment.UserName },
                        { "Computer Name", Environment.MachineName }
                    });

                try
                {
                    // Your command logic here
                    DoSomething(doc);

                    // Track success
                    telemetry.TrackEvent("My Command Completed", 
                        new Dictionary<string, string> { { "Status", "Success" } });

                    return Result.Succeeded;
                }
                catch (Exception ex)
                {
                    // Track exception
                    telemetry.TrackException(ex, "MyCommand.Execute");

                    telemetry.TrackEvent("My Command Failed",
                        new Dictionary<string, string> { { "Error", ex.Message } });

                    message = ex.Message;
                    return Result.Failed;
                }
                finally
                {
                    // Ensure telemetry is sent before command ends
                    telemetry.Flush();
                }
            }
        }

        private void DoSomething(Document doc)
        {
            // Your Revit logic here
        }
    }
}
```

## Integration with BecaBaseCommand

If you're using the BecaBaseCommand pattern, here's how to integrate:

### Option 1: Replace TelemetryHandler with TelemetryClientSync

```csharp
using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaTelemetryClient;
using System;
using System.Collections.Generic;

namespace BecaCommand
{
    [Transaction(TransactionMode.Manual)]
    public abstract class BecaBaseCommand : IExternalCommand
    {
        protected TelemetryClientSync telemetry;
        private DateTime startTime;

        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            UIApplication uiapp = commandData.Application;
            UIDocument uidoc = uiapp.ActiveUIDocument;
            Document doc = uidoc.Document;

            var addinName = GetAddinName();
            var commandSubName = GetCommandSubName();

            // Initialize telemetry (replaces the old TelemetryHandler)
            try
            {
                telemetry = new TelemetryClientSync("http://your-telemetry-service:8080");
                ProcessingStarter(doc, addinName, commandSubName);
            }
            catch (Exception ex)
            {
                // Telemetry failed, but don't break the command
                TaskDialog.Show("Telemetry Warning", 
                    "Unable to connect to telemetry service. Command will continue.");
            }

            Result becaCommandResult;
            try
            {
                becaCommandResult = ExecuteBecaCommand(commandData, ref message, elements);
            }
            catch (Exception ex)
            {
                becaCommandResult = Result.Failed;
                telemetry?.TrackException(ex, $"{addinName}.ExecuteBecaCommand");
            }

            // Track completion
            switch (becaCommandResult)
            {
                case Result.Failed:
                    ProcessingFailed(addinName, commandSubName);
                    break;
                case Result.Succeeded:
                    ProcessingCompleted(addinName, commandSubName);
                    break;
                case Result.Cancelled:
                    ProcessingCancelled(addinName, commandSubName);
                    break;
            }

            telemetry?.Dispose();
            return becaCommandResult;
        }

        private void ProcessingStarter(Document doc, string addinName, string commandSubName)
        {
            startTime = DateTime.UtcNow;
            var eventName = string.IsNullOrEmpty(commandSubName) 
                ? $"{addinName} Started" 
                : $"{addinName}|{commandSubName} Started";

            var properties = new Dictionary<string, string>
            {
                { "Computer Name", Environment.MachineName },
                { "User Name", Environment.UserName },
                { "Project Number", doc.ProjectInformation.Number },
                { "Model Name", doc.Title },
                { "Revit Version", doc.Application.VersionNumber }
            };

            telemetry?.TrackEvent(eventName, properties);
        }

        private void ProcessingCompleted(string addinName, string commandSubName)
        {
            var eventName = string.IsNullOrEmpty(commandSubName)
                ? $"{addinName} Ended"
                : $"{addinName}|{commandSubName} Ended";

            var duration = (DateTime.UtcNow - startTime).TotalSeconds;
            var properties = new Dictionary<string, string>
            {
                { "Consumed Time", TimeSpan.FromSeconds(duration).ToString(@"hh\:mm\:ss") },
                { "Process Status", "Completed" }
            };

            telemetry?.TrackEvent(eventName, properties);
            telemetry?.Flush();
        }

        private void ProcessingFailed(string addinName, string commandSubName)
        {
            var eventName = string.IsNullOrEmpty(commandSubName)
                ? $"{addinName} Ended"
                : $"{addinName}|{commandSubName} Ended";

            var duration = (DateTime.UtcNow - startTime).TotalSeconds;
            var properties = new Dictionary<string, string>
            {
                { "Consumed Time", TimeSpan.FromSeconds(duration).ToString(@"hh\:mm\:ss") },
                { "Process Status", "Failed" }
            };

            telemetry?.TrackEvent(eventName, properties);
            telemetry?.Flush();
        }

        private void ProcessingCancelled(string addinName, string commandSubName)
        {
            var eventName = string.IsNullOrEmpty(commandSubName)
                ? $"{addinName} Ended"
                : $"{addinName}|{commandSubName} Ended";

            var duration = (DateTime.UtcNow - startTime).TotalSeconds;
            var properties = new Dictionary<string, string>
            {
                { "Consumed Time", TimeSpan.FromSeconds(duration).ToString(@"hh\:mm\:ss") },
                { "Process Status", "Canceled" }
            };

            telemetry?.TrackEvent(eventName, properties);
            telemetry?.Flush();
        }

        public abstract Result ExecuteBecaCommand(ExternalCommandData commandData, ref string message, ElementSet elements);
        public abstract string GetAddinName();
        public abstract string GetCommandSubName();
        public abstract string GetAddinAuthor();
    }
}
```

### Option 2: Use the BecaBaseCommandWithTelemetry Helper

The `TelemetryClientSync.cs` file includes a `BecaBaseCommandWithTelemetry` class that mimics the original TelemetryHandler API:

```csharp
using BecaTelemetryClient;

// In your BecaBaseCommand.Execute method:
var telemetryHelper = new BecaBaseCommandWithTelemetry();
telemetryHelper.InitializeTelemetry(addinName, commandSubName, "http://your-service:8080");

// Track start
telemetryHelper.ProcessingStarter(new Dictionary<string, string>
{
    { "Revit Version", doc.Application.VersionNumber },
    { "Project Number", doc.ProjectInformation.Number }
});

try
{
    // Your command logic
    var result = ExecuteBecaCommand(commandData, ref message, elements);
    
    if (result == Result.Succeeded)
        telemetryHelper.ProcessingCompleted();
    else if (result == Result.Failed)
        telemetryHelper.ProcessingFailed();
    else
        telemetryHelper.ProcessingCancelled();
        
    return result;
}
catch (Exception ex)
{
    telemetryHelper.LogException(ex, "ExecuteBecaCommand");
    telemetryHelper.ProcessingFailed();
    throw;
}
finally
{
    telemetryHelper.Dispose();
}
```

## Configuration

### Service URL Configuration

Store the telemetry service URL in your add-in settings:

```csharp
// In app.config or settings file
<appSettings>
  <add key="TelemetryServiceUrl" value="http://your-telemetry-service:8080"/>
</appSettings>

// In code
var serviceUrl = ConfigurationManager.AppSettings["TelemetryServiceUrl"] 
    ?? "http://localhost:5004";
var telemetry = new TelemetryClientSync(serviceUrl);
```

### Conditional Telemetry (Development vs Production)

```csharp
#if DEBUG
    // Don't send telemetry in debug mode
    TelemetryClientSync telemetry = null;
#else
    var telemetry = new TelemetryClientSync("http://your-service:8080");
#endif

// Use null-conditional operator
telemetry?.TrackEvent("Event Name", properties);
```

## Best Practices

### 1. Always Use `using` Statement

```csharp
using (var telemetry = new TelemetryClientSync("http://service:8080"))
{
    // Your code
    telemetry.Flush();
} // Automatically disposes
```

### 2. Always Call Flush Before Command Ends

```csharp
try
{
    // Command logic
}
finally
{
    telemetry?.Flush(); // Ensure telemetry is sent
}
```

### 3. Don't Let Telemetry Break Your Command

```csharp
try
{
    telemetry = new TelemetryClientSync("http://service:8080");
}
catch (Exception ex)
{
    // Log but continue
    Console.WriteLine($"Telemetry failed: {ex.Message}");
    telemetry = null;
}

// Use null-conditional operator everywhere
telemetry?.TrackEvent("Event", properties);
```

### 4. Track Important Metrics

```csharp
// Track processing time
var startTime = DateTime.UtcNow;
// ... do work ...
var duration = (DateTime.UtcNow - startTime).TotalSeconds;
telemetry.TrackMetric("Processing Time", duration);

// Track element counts
telemetry.TrackMetric("Elements Processed", elementCount);
```

### 5. Include Context in Properties

```csharp
var properties = new Dictionary<string, string>
{
    { "Revit Version", doc.Application.VersionNumber },
    { "Project Number", doc.ProjectInformation.Number },
    { "Model Name", doc.Title },
    { "User Name", Environment.UserName },
    { "Computer Name", Environment.MachineName },
    { "Organization", doc.ProjectInformation.OrganizationName },
    { "Building Name", doc.ProjectInformation.BuildingName }
};
```

## Troubleshooting

### Issue: Telemetry Service Not Reachable

**Solution**: Use try-catch and null-conditional operators:

```csharp
TelemetryClientSync telemetry = null;
try
{
    telemetry = new TelemetryClientSync("http://service:8080");
}
catch
{
    // Service not available, continue without telemetry
}

telemetry?.TrackEvent("Event", properties);
```

### Issue: Slow Performance

**Solution**: The synchronous client uses `.Result` which blocks. This is intentional for Revit. If you experience slowness:

1. Check network connectivity to telemetry service
2. Ensure service is responding quickly (< 100ms)
3. Consider reducing telemetry frequency

### Issue: Revit Hangs

**Solution**: Make sure you're calling `Flush()` and `Dispose()`:

```csharp
try
{
    // Your code
}
finally
{
    telemetry?.Flush();
    telemetry?.Dispose();
}
```

## Complete Example

Here's a complete, production-ready example:

```csharp
using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaTelemetryClient;
using System;
using System.Collections.Generic;
using System.Configuration;

namespace MyRevitAddin
{
    [Transaction(TransactionMode.Manual)]
    public class MyProductionCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            TelemetryClientSync telemetry = null;
            var startTime = DateTime.UtcNow;

            try
            {
                // Initialize telemetry
                var serviceUrl = ConfigurationManager.AppSettings["TelemetryServiceUrl"] 
                    ?? "http://localhost:5004";
                telemetry = new TelemetryClientSync(serviceUrl);

                // Get Revit context
                var doc = commandData.Application.ActiveUIDocument.Document;

                // Track start
                telemetry.TrackEvent("My Command Started", new Dictionary<string, string>
                {
                    { "Revit Version", doc.Application.VersionNumber },
                    { "Project Number", doc.ProjectInformation.Number },
                    { "Model Name", doc.Title }
                });

                // Execute command logic
                var result = ExecuteCommandLogic(doc, telemetry);

                // Track completion
                var duration = (DateTime.UtcNow - startTime).TotalSeconds;
                telemetry.TrackMetric("Command Duration", duration);
                telemetry.TrackEvent("My Command Completed", new Dictionary<string, string>
                {
                    { "Status", result.ToString() },
                    { "Duration", duration.ToString("F2") }
                });

                return result;
            }
            catch (Exception ex)
            {
                // Track exception
                telemetry?.TrackException(ex, "MyProductionCommand.Execute");
                telemetry?.TrackEvent("My Command Failed", new Dictionary<string, string>
                {
                    { "Error", ex.Message }
                });

                message = ex.Message;
                return Result.Failed;
            }
            finally
            {
                // Always flush and dispose
                telemetry?.Flush();
                telemetry?.Dispose();
            }
        }

        private Result ExecuteCommandLogic(Document doc, TelemetryClientSync telemetry)
        {
            // Your Revit command logic here
            return Result.Succeeded;
        }
    }
}
```

## Next Steps

1. Copy `TelemetryClientSync.cs` to your project
2. Update your BecaBaseCommand to use the new client
3. Configure the service URL
4. Test with a simple command
5. Deploy to all your Revit add-ins

## Support

For issues or questions:
- Check the main [README.md](../../README.md)
- Review [TESTING.md](../../TESTING.md)
- Contact the development team

