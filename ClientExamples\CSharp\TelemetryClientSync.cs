using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;

namespace BecaTelemetryClient
{
    /// <summary>
    /// Synchronous client for sending telemetry to BecaTracktion microservice
    /// Specifically designed for Revit add-ins and other applications that don't work well with async/await
    /// </summary>
    public class TelemetryClientSync : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private readonly string _userId;
        private readonly string _sessionId;

        public TelemetryClientSync(string baseUrl, string userId = null)
        {
            _baseUrl = baseUrl.TrimEnd(new[] { '/' });
            _httpClient = new HttpClient
            {
                Timeout = TimeSpan.FromSeconds(10)
            };
            _userId = userId ?? Environment.UserName;
            _sessionId = Guid.NewGuid().ToString();
        }

        /// <summary>
        /// Track a custom event (synchronous)
        /// </summary>
        public bool TrackEvent(
            string eventName,
            Dictionary<string, string> properties = null,
            Dictionary<string, double> metrics = null)
        {
            try
            {
                var telemetryEvent = new
                {
                    EventName = eventName,
                    Properties = properties ?? new Dictionary<string, string>(),
                    Metrics = metrics,
                    UserId = _userId,
                    SessionId = _sessionId,
                    Timestamp = DateTime.UtcNow
                };

                var json = JsonSerializer.Serialize(telemetryEvent);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = _httpClient.PostAsync(
                    $"{_baseUrl}/api/telemetry/event",
                    content).Result;

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error tracking event: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Track an exception (synchronous)
        /// </summary>
        public bool TrackException(
            Exception exception,
            string location = null,
            Dictionary<string, string> properties = null)
        {
            try
            {
                var telemetryException = new
                {
                    Message = exception.Message,
                    ExceptionType = exception.GetType().FullName,
                    StackTrace = exception.StackTrace,
                    Location = location,
                    Properties = properties ?? new Dictionary<string, string>(),
                    UserId = _userId,
                    SessionId = _sessionId,
                    Timestamp = DateTime.UtcNow
                };

                var json = JsonSerializer.Serialize(telemetryException);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = _httpClient.PostAsync(
                    $"{_baseUrl}/api/telemetry/exception",
                    content).Result;

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error tracking exception: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Track a metric (synchronous)
        /// </summary>
        public bool TrackMetric(
            string metricName,
            double value,
            Dictionary<string, string> properties = null)
        {
            try
            {
                var telemetryMetric = new
                {
                    MetricName = metricName,
                    Value = value,
                    Properties = properties,
                    UserId = _userId,
                    SessionId = _sessionId,
                    Timestamp = DateTime.UtcNow
                };

                var json = JsonSerializer.Serialize(telemetryMetric);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = _httpClient.PostAsync(
                    $"{_baseUrl}/api/telemetry/metric",
                    content).Result;

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error tracking metric: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Track a page view (synchronous)
        /// </summary>
        public bool TrackPageView(
            string pageName,
            Dictionary<string, string> properties = null)
        {
            try
            {
                var telemetryPageView = new
                {
                    PageName = pageName,
                    Properties = properties,
                    UserId = _userId,
                    SessionId = _sessionId,
                    Timestamp = DateTime.UtcNow
                };

                var json = JsonSerializer.Serialize(telemetryPageView);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = _httpClient.PostAsync(
                    $"{_baseUrl}/api/telemetry/pageview",
                    content).Result;

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error tracking page view: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Flush telemetry to ensure it's sent (synchronous)
        /// </summary>
        public bool Flush()
        {
            try
            {
                var response = _httpClient.PostAsync(
                    $"{_baseUrl}/api/telemetry/flush",
                    null).Result;

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error flushing telemetry: {ex.Message}");
                return false;
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    /// <summary>
    /// Example usage in a Revit add-in (synchronous version)
    /// This works perfectly with Revit's IExternalCommand.Execute method
    /// </summary>
    public class RevitCommandExampleSync
    {
        public void ExecuteCommand()
        {
            using (var telemetry = new TelemetryClientSync("http://localhost:5004"))
            {
                var startTime = DateTime.UtcNow;

                // Track command start
                telemetry.TrackEvent(
                    "Revit Command Started",
                    new Dictionary<string, string>
                    {
                        { "Command", "MyRevitCommand" },
                        { "Revit Version", "2024" },
                        { "Computer Name", Environment.MachineName },
                        { "User Name", Environment.UserName }
                    });

                try
                {
                    // Your command logic here
                    System.Threading.Thread.Sleep(1000); // Simulate work

                    // Track success
                    var duration = (DateTime.UtcNow - startTime).TotalSeconds;
                    telemetry.TrackMetric("Command Duration", duration);

                    telemetry.TrackEvent(
                        "Revit Command Completed",
                        new Dictionary<string, string>
                        {
                            { "Status", "Success" },
                            { "Duration", duration.ToString("F2") }
                        });
                }
                catch (Exception ex)
                {
                    // Track exception
                    telemetry.TrackException(
                        ex,
                        "RevitCommandExampleSync.ExecuteCommand",
                        new Dictionary<string, string>
                        {
                            { "Command", "MyRevitCommand" }
                        });

                    telemetry.TrackEvent(
                        "Revit Command Failed",
                        new Dictionary<string, string>
                        {
                            { "Error", ex.Message }
                        });
                }
                finally
                {
                    // Ensure telemetry is sent
                    telemetry.Flush();
                }
            }
        }
    }

    /// <summary>
    /// Example: Integrating with BecaBaseCommand pattern
    /// Drop-in replacement for the original TelemetryHandler
    /// </summary>
    public class BecaBaseCommandWithTelemetry
    {
        private TelemetryClientSync telemetry;
        private DateTime startTime;
        private string addinName;
        private string commandSubName;

        public void InitializeTelemetry(string addinName, string commandSubName, string serviceUrl = "http://localhost:5004")
        {
            this.addinName = addinName;
            this.commandSubName = commandSubName;
            
            try
            {
                telemetry = new TelemetryClientSync(serviceUrl);
            }
            catch (Exception ex)
            {
                // Telemetry initialization failed, but don't break the command
                Console.WriteLine($"Telemetry initialization failed: {ex.Message}");
            }
        }

        public void ProcessingStarter(Dictionary<string, string> properties)
        {
            if (telemetry == null) return;

            startTime = DateTime.UtcNow;
            var eventName = string.IsNullOrEmpty(commandSubName) 
                ? $"{addinName} Started" 
                : $"{addinName}|{commandSubName} Started";

            telemetry.TrackEvent(eventName, properties);
        }

        public void ProcessingCompleted()
        {
            if (telemetry == null) return;

            var eventName = string.IsNullOrEmpty(commandSubName)
                ? $"{addinName} Ended"
                : $"{addinName}|{commandSubName} Ended";

            var duration = (DateTime.UtcNow - startTime).TotalSeconds;
            var properties = new Dictionary<string, string>
            {
                { "Consumed Time", TimeSpan.FromSeconds(duration).ToString(@"hh\:mm\:ss") },
                { "Process Status", "Completed" }
            };

            telemetry.TrackEvent(eventName, properties);
            telemetry.Flush();
        }

        public void ProcessingFailed()
        {
            if (telemetry == null) return;

            var eventName = string.IsNullOrEmpty(commandSubName)
                ? $"{addinName} Ended"
                : $"{addinName}|{commandSubName} Ended";

            var duration = (DateTime.UtcNow - startTime).TotalSeconds;
            var properties = new Dictionary<string, string>
            {
                { "Consumed Time", TimeSpan.FromSeconds(duration).ToString(@"hh\:mm\:ss") },
                { "Process Status", "Failed" }
            };

            telemetry.TrackEvent(eventName, properties);
            telemetry.Flush();
        }

        public void ProcessingCancelled()
        {
            if (telemetry == null) return;

            var eventName = string.IsNullOrEmpty(commandSubName)
                ? $"{addinName} Ended"
                : $"{addinName}|{commandSubName} Ended";

            var duration = (DateTime.UtcNow - startTime).TotalSeconds;
            var properties = new Dictionary<string, string>
            {
                { "Consumed Time", TimeSpan.FromSeconds(duration).ToString(@"hh\:mm\:ss") },
                { "Process Status", "Canceled" }
            };

            telemetry.TrackEvent(eventName, properties);
            telemetry.Flush();
        }

        public void LogException(Exception ex, string location)
        {
            if (telemetry == null) return;

            telemetry.TrackException(ex, location);
        }

        public void LogProcessing(Dictionary<string, string> properties)
        {
            if (telemetry == null) return;

            var eventName = string.IsNullOrEmpty(commandSubName)
                ? $"{addinName} Processing"
                : $"{addinName}|{commandSubName} Processing";

            telemetry.TrackEvent(eventName, properties);
        }

        public void Dispose()
        {
            telemetry?.Dispose();
        }
    }
}

