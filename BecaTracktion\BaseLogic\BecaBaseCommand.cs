#region Namespaces
using System;
using System.Collections.Generic;
using System.Diagnostics;
using Autodesk.Revit.ApplicationServices;
using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Autodesk.Revit.UI.Selection;
using System.Linq;
using System.Text;
using System.Windows;

#endregion

namespace BecaCommand
{
    [Transaction(TransactionMode.Manual)]
    public abstract class BecaBaseCommand : IExternalCommand
    {
        #region Fields

        protected TelemetryClient telemetry;
        protected TrekaHandler treka;
        protected BecaActivityLoggerData _taskLogger;

        #endregion

        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {

            UIApplication uiapp = commandData.Application;
            UIDocument uidoc = uiapp.ActiveUIDocument;
            Application app = uiapp.Application;
            Document doc = uidoc.Document;

            #region Treka
            var addinName = GetAddinName();
            var commandSubName = GetCommandSubName();
            treka = new TrekaHandler(addinName, commandSubName, doc);
            treka.ProcessingStarter();

            #endregion

            #region Activity Logger

            var logDataHandler = BecaActivityLoggerDataHandler.GetBecaActivityLoggerDataHandler();
            _taskLogger = logDataHandler.addNewTaskLogger(addinName);

            #endregion


            Result becaCommandResult;
            try
            {
                becaCommandResult = ExecuteBecaCommand(commandData, ref message, elements);
            }
            catch (Exception ex)
            {
                becaCommandResult = Result.Failed;
                _taskLogger.Log(ex.Message, LogType.Error);
                _taskLogger.PostTaskEnd(GetAddinName()+ " failed to excute. Check detailed Logs.");
                var frame = new StackTrace(ex, true).GetFrame(0);
                //telemetry?.LogException(ex, string.Format("Exception in File {0} at line {1}", frame.GetFileName(), frame.GetFileLineNumber()));
            }


            //logging beca activity logs to telmetry. shall be refactored later and this will be removed.
            var logs=_taskLogger.Logs.Select(l => l.DateTime + "\t" + l.Message + "\t" + l.LogType.ToString());
            var logsDic=new Dictionary<string, string>();
            logsDic.Add("Logs", string.Join(Environment.NewLine, logs));
            //telemetry?.LogProcessing(logsDic);

            switch (becaCommandResult)
            {
                case Result.Failed:
                    treka.ProcessingFailed();
                    //telemetry?.ProcessingFailed();
                    break;
                case Result.Succeeded:
                    treka.ProcessingCompleted();
                    //telemetry?.ProcessingCompleted();
                    break;
                case Result.Cancelled:
                    treka.ProcessingCancelled();
                    //telemetry?.ProcessingCancelled();
                    break;
                default:
                    break;
            }

            return becaCommandResult;
        }

        #region Abstract Methods 

        abstract public Result ExecuteBecaCommand(ExternalCommandData commandData, ref string message, ElementSet elements);

        abstract public string GetAddinName();
        /// <summary>
        /// For add-ins having multiple commands otherwise return string.Empty()
        /// </summary>
        /// <returns></returns>
        abstract public string GetCommandSubName();

        abstract public string GetAddinAuthor();

        #endregion

        //        {
        //            UIApplication uiapp = commandData.Application;
        //            UIDocument uidoc = uiapp.ActiveUIDocument;
        //            Application app = uiapp.Application;
        //            Document doc = uidoc.Document;

        //            string rvtVerision = string.Empty;

        //#if TargetYear2014
        //            //some code works only on revit verison 2014
        //            //only if there upgrades or changes made by Autodesk in their API
        //             rvtVerision = "2014";

        //#elif TargetYear2015
        //            //some code works only on revit verison 2015
        //            //only if there upgrades or changes made by Autodesk in their API
        //#elif TargetYear2016
        //            //some code works only on revit verison 2016
        //            //only if there upgrades or changes made by Autodesk in their API
        //#elif TargetYear2017
        //            //some code works only on revit verison 2017
        //            //only if there upgrades or changes made by Autodesk in their API
        //             rvtVerision = "2017";

        //#elif TargetYear2018
        //            //some code works only on revit verison 2018
        //            //only if there upgrades or changes made by Autodesk in their API
        //             rvtVerision = "2018";

        //#elif TargetYear2019
        //            //some code works only on revit verison 2019
        //            //only if there upgrades or changes made by Autodesk in their API
        //            rvtVerision = "2019";

        //#elif TargetYear2020
        //            //some code works only on revit verison 2020
        //            //only if there upgrades or changes made by Autodesk in their API
        //             rvtVerision = "2020";

        //#elif TargetYear2021
        //            //some code works only on revit verison 2021
        //            //only if there upgrades or changes made by Autodesk in their API
        //#endif

        //            TaskDialog mainDialog = new TaskDialog($"Hello, Revit {rvtVerision}!");
        //            mainDialog.Show();
        //            return Result.Succeeded;
        //        }
    }
}
