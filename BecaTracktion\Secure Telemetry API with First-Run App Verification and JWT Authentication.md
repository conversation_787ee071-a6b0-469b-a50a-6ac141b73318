I have a telemetry system where client applications (like Revit add-ins) send telemetry data to a backend API. I want to secure this API using JWT token-based authentication, but with an additional layer of first-run app verification to prevent misuse of credentials across unauthorized apps.

Here's what I need:

🔐 Security Goals
JWT Authentication:
Implement a token issuance endpoint (/api/auth/token) that accepts ClientId and Secret.
Tokens should include claims like client_id, role, and expire after 1 hour.
First-Run App Verification:
On first run, the client app should collect metadata such as:
App GUID
App Name
Machine ID (or hashed fingerprint)
This metadata should be sent along with the token request.
The server should store this metadata and bind it to the issued token.
Token Binding and Validation:
Embed the app metadata in the JWT claims.
On every telemetry request, validate that the token’s metadata matches the current app context.
Reject requests if the token is used from a different app or machine.
Client Integration
Modify TracktionClient.cs to:
Collect app metadata on first run.
Request a token using ClientId, Secret, and metadata.
Include the token in the Authorization header for all telemetry requests.
Server-Side Enforcement:
Secure telemetry endpoints (e.g., /api/telemetry/event) using [Authorize].
Validate token claims and compare with incoming metadata.
Documentation:
Include a README.md explaining:
How the verification and token flow works
How to request and use a token
How the server validates and enforces token binding
I will attach the current implementation of TracktionClient.cs, TelemetryController.cs, and ApplicationInsightsTelemetryService.cs. Please integrate the new security logic into this structure.