# Use the official .NET 8 SDK image for building
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy the project file and restore dependencies
COPY ["BecaTracktion/BecaTracktion.csproj", "BecaTracktion/"]
RUN dotnet restore "BecaTracktion/BecaTracktion.csproj"

# Copy the rest of the source code
COPY . .
WORKDIR "/src/BecaTracktion"

# Build the application
RUN dotnet build "BecaTracktion.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "BecaTracktion.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Use the official .NET 8 runtime image for running
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final
WORKDIR /app

# Expose ports
EXPOSE 80
EXPOSE 443

# Copy the published application
COPY --from=publish /app/publish .

# Set the entry point
ENTRYPOINT ["dotnet", "BecaTracktion.dll"]

