# BecaTracktion Telemetry Microservice - Deployment Guide

This guide provides detailed instructions for deploying the BecaTracktion Telemetry Microservice in various environments.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Local Development](#local-development)
3. [Docker Deployment](#docker-deployment)
4. [Azure App Service](#azure-app-service)
5. [Kubernetes Deployment](#kubernetes-deployment)
6. [Configuration](#configuration)
7. [Monitoring](#monitoring)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

### Required
- .NET 8.0 SDK (for local development)
- Docker (for containerized deployment)
- Azure Application Insights resource

### Optional
- Azure subscription (for cloud deployment)
- Kubernetes cluster (for K8s deployment)
- Azure CLI
- kubectl

## Local Development

### Step 1: Clone and Restore

```bash
# Navigate to project directory
cd "BecaTracktion API"

# Restore NuGet packages
dotnet restore BecaTracktion/BecaTracktion.csproj
```

### Step 2: Configure Application Insights

Edit `BecaTracktion/appsettings.Development.json`:

```json
{
  "ApplicationInsights": {
    "ConnectionString": "YOUR_CONNECTION_STRING_HERE"
  }
}
```

### Step 3: Run the Application

```bash
# Run the application
dotnet run --project BecaTracktion

# Or with hot reload
dotnet watch run --project BecaTracktion
```

### Step 4: Access the Application

- Swagger UI: http://localhost:5004
- API Base URL: http://localhost:5004/api/telemetry
- Health Check: http://localhost:5004/api/telemetry/health

## Docker Deployment

### Option 1: Using Docker CLI

#### Build the Image

```bash
# Build the Docker image
docker build -t becatracktion:latest .

# Verify the image
docker images | grep becatracktion
```

#### Run the Container

```bash
# Run with default configuration
docker run -d \
  --name becatracktion \
  -p 8080:80 \
  becatracktion:latest

# Run with custom Application Insights connection string
docker run -d \
  --name becatracktion \
  -p 8080:80 \
  -e ApplicationInsights__ConnectionString="YOUR_CONNECTION_STRING" \
  becatracktion:latest

# Run with environment variables from file
docker run -d \
  --name becatracktion \
  -p 8080:80 \
  --env-file .env \
  becatracktion:latest
```

#### Verify the Container

```bash
# Check container status
docker ps | grep becatracktion

# View logs
docker logs becatracktion

# Follow logs
docker logs -f becatracktion

# Test health endpoint
curl http://localhost:8080/api/telemetry/health
```

#### Stop and Remove

```bash
# Stop the container
docker stop becatracktion

# Remove the container
docker rm becatracktion

# Remove the image
docker rmi becatracktion:latest
```

### Option 2: Using Docker Compose

#### Create Environment File

Create a `.env` file in the project root:

```env
APP_INSIGHTS_CONNECTION_STRING=YOUR_CONNECTION_STRING_HERE
```

#### Start Services

```bash
# Start in detached mode
docker-compose up -d

# Start with build
docker-compose up -d --build

# View logs
docker-compose logs -f

# Check status
docker-compose ps
```

#### Stop Services

```bash
# Stop services
docker-compose down

# Stop and remove volumes
docker-compose down -v
```

## Azure App Service (Detailed Step-by-Step Guide)

### Understanding Azure App Service Options

When creating a new web app in Azure Portal, you'll see several options:

| Option | Best For | Description |
|--------|----------|-------------|
| **Web App** ✅ | ASP.NET Core APIs, Web Apps | **Choose this for BecaTracktion!** Full-featured web hosting with .NET 8 support |
| Static Web App | Static HTML/JS, SPAs | For static content only (React, Angular, Vue) - NOT for ASP.NET Core APIs |
| Web App + Database | Apps needing SQL Database | Web App bundled with Azure SQL Database |
| WordPress on App Service | WordPress sites | Pre-configured WordPress hosting |

**For BecaTracktion Telemetry Microservice, choose: Web App**

---

### Method 1: Deploy Using Azure Portal (Recommended for First-Time)

This is the easiest method with a visual interface.

#### Step 1: Sign in to Azure Portal

1. Go to [https://portal.azure.com](https://portal.azure.com)
2. Sign in with your Azure account
3. If you don't have an account, create a free one (includes $200 credit)

#### Step 2: Create a Web App

1. **Search for App Services**
   - In the top search bar, type "App Services"
   - Click on "App Services" under Services

2. **Click "Create"**
   - Click the "+ Create" button
   - Select "Web App" (NOT Static Web App)

3. **Configure Basic Settings (Basics Tab)**

   Fill in the following:

   | Setting | Value | Notes |
   |---------|-------|-------|
   | **Subscription** | Your subscription | Select your Azure subscription |
   | **Resource Group** | Create new: `rg-becatracktion` | Click "Create new" and enter the name |
   | **Name** | `becatracktion-telemetry` | Must be globally unique. Try adding numbers if taken |
   | **Publish** | **Code** ✅ | Select "Code" (not Container) |
   | **Runtime stack** | **.NET 8 (LTS)** ✅ | Select from dropdown |
   | **Operating System** | **Windows** or **Linux** | Windows recommended for .NET |
   | **Region** | **Australia East** | Choose closest to your users |

4. **Configure App Service Plan**

   Still in the Basics tab:

   | Setting | Value | Notes |
   |---------|-------|-------|
   | **Windows Plan** | Create new: `plan-becatracktion` | Click "Create new" |
   | **Pricing plan** | **Basic B1** | Click "Explore pricing plans" to see options |

   **Pricing Tiers:**
   - **Free F1**: Free, but limited (60 min/day, no custom domain, no SSL)
   - **Basic B1**: ~$13/month, always on, custom domain, SSL ✅ **Recommended**
   - **Standard S1**: ~$70/month, auto-scaling, staging slots
   - **Premium P1V2**: ~$100/month, enhanced performance

   For production, use **Basic B1** or higher.

5. **Click "Next: Deployment >"**

#### Step 3: Configure Deployment (Optional but Recommended)

If you want automatic deployment from GitHub:

1. **Enable Continuous Deployment**
   - Set "Continuous deployment" to **Enable**
   - Click "Authorize" to connect your GitHub account

2. **Configure GitHub Actions**
   - **Organization**: Your GitHub username/org
   - **Repository**: Select your repository (if you forked the project)
   - **Branch**: `main`

   > **Note**: This creates a GitHub Actions workflow for automatic deployment. You can skip this and deploy manually later.

3. **Click "Next: Networking >"**

#### Step 4: Configure Networking (Use Defaults)

1. Leave "Enable public access" as **On**
2. Click "Next: Monitoring >"

#### Step 5: Configure Monitoring

1. **Enable Application Insights**: **Yes** ✅
2. **Application Insights**: Create new or select existing
3. **Region**: Same as your Web App
4. Click "Next: Tags >"

#### Step 6: Add Tags (Optional)

Add tags for organization:
- `Environment`: `Production`
- `Project`: `BecaTracktion`
- `Owner`: Your name

Click "Next: Review + create >"

#### Step 7: Review and Create

1. Review all settings
2. Click "Create"
3. Wait 1-2 minutes for deployment to complete
4. Click "Go to resource"

#### Step 8: Configure Application Settings

Now configure the Application Insights connection string:

1. In your App Service, click **"Configuration"** in the left menu
2. Click **"+ New application setting"**
3. Add the following:

   | Name | Value |
   |------|-------|
   | `ApplicationInsights__ConnectionString` | Your Application Insights connection string |

   > **To get your connection string:**
   > - Go to your Application Insights resource
   > - Click "Overview"
   > - Copy the "Connection String"

4. Click **"OK"**
5. Click **"Save"** at the top
6. Click **"Continue"** when prompted

#### Step 9: Deploy Your Application

**Option A: Deploy from Visual Studio 2022**

1. Open your BecaTracktion solution in Visual Studio
2. Right-click the `BecaTracktion` project
3. Select **"Publish..."**
4. Click **"Azure"** → **"Next"**
5. Select **"Azure App Service (Windows)"** or **"Azure App Service (Linux)"** → **"Next"**
6. Sign in to Azure if prompted
7. Select your subscription and the `becatracktion-telemetry` app
8. Click **"Finish"**
9. Click **"Publish"**

**Option B: Deploy from Visual Studio Code**

1. Install the "Azure App Service" extension
2. Open your project in VS Code
3. Click the Azure icon in the sidebar
4. Sign in to Azure
5. Right-click your App Service
6. Select **"Deploy to Web App..."**
7. Select the `BecaTracktion` folder
8. Confirm deployment

**Option C: Deploy using Azure CLI** (see Method 2 below)

**Option D: Deploy using ZIP file**

1. Publish your app locally:
   ```bash
   dotnet publish BecaTracktion/BecaTracktion.csproj -c Release -o ./publish
   ```

2. Create a ZIP file of the publish folder

3. In Azure Portal:
   - Go to your App Service
   - Click **"Deployment Center"** in the left menu
   - Click **"FTPS credentials"** tab
   - Note the FTPS endpoint and credentials

4. Or use Azure CLI:
   ```bash
   cd publish
   zip -r ../deploy.zip .
   az webapp deployment source config-zip \
     --resource-group rg-becatracktion \
     --name becatracktion-telemetry \
     --src ../deploy.zip
   ```

#### Step 10: Verify Deployment

1. In Azure Portal, go to your App Service
2. Click **"Overview"**
3. Click the **URL** (e.g., `https://becatracktion-telemetry.azurewebsites.net`)
4. You should see the Swagger UI
5. Test the health endpoint:
   ```
   https://becatracktion-telemetry.azurewebsites.net/api/telemetry/health
   ```

---

### Method 2: Deploy Using Azure CLI (For Automation)

This method is faster and can be scripted.

#### Prerequisites

- Azure CLI installed: [Download here](https://docs.microsoft.com/cli/azure/install-azure-cli)
- .NET 8 SDK installed

#### Step 1: Login to Azure

```bash
# Login to Azure
az login

# Set your subscription (if you have multiple)
az account set --subscription "Your Subscription Name"

# Verify
az account show
```

#### Step 2: Create Resource Group

```bash
# Create resource group in Australia East
az group create \
  --name rg-becatracktion \
  --location australiaeast

# Verify
az group show --name rg-becatracktion
```

#### Step 3: Create App Service Plan

```bash
# Create App Service Plan (Basic B1 tier)
az appservice plan create \
  --name plan-becatracktion \
  --resource-group rg-becatracktion \
  --location australiaeast \
  --sku B1 \
  --is-linux

# For Windows, remove --is-linux:
az appservice plan create \
  --name plan-becatracktion \
  --resource-group rg-becatracktion \
  --location australiaeast \
  --sku B1

# Verify
az appservice plan show \
  --name plan-becatracktion \
  --resource-group rg-becatracktion
```

**Pricing Tiers:**
- `F1` - Free (limited)
- `B1` - Basic ($13/month) ✅ Recommended
- `S1` - Standard ($70/month)
- `P1V2` - Premium ($100/month)

#### Step 4: Create Web App

```bash
# Create Web App with .NET 8 runtime
az webapp create \
  --name becatracktion-telemetry \
  --resource-group rg-becatracktion \
  --plan plan-becatracktion \
  --runtime "DOTNET:8"

# For Windows:
az webapp create \
  --name becatracktion-telemetry \
  --resource-group rg-becatracktion \
  --plan plan-becatracktion \
  --runtime "DOTNET|8"

# Verify
az webapp show \
  --name becatracktion-telemetry \
  --resource-group rg-becatracktion \
  --query "{name:name, state:state, defaultHostName:defaultHostName}"
```

#### Step 5: Configure Application Settings

```bash
# Set Application Insights connection string
az webapp config appsettings set \
  --name becatracktion-telemetry \
  --resource-group rg-becatracktion \
  --settings ApplicationInsights__ConnectionString="YOUR_CONNECTION_STRING_HERE"

# Set other settings
az webapp config appsettings set \
  --name becatracktion-telemetry \
  --resource-group rg-becatracktion \
  --settings ASPNETCORE_ENVIRONMENT="Production"

# Verify
az webapp config appsettings list \
  --name becatracktion-telemetry \
  --resource-group rg-becatracktion
```

#### Step 6: Publish and Deploy

```bash
# Navigate to your project directory
cd "c:\Users\<USER>\Documents\Folders\TRACKTION\BecaTracktion API"

# Publish the application
dotnet publish BecaTracktion/BecaTracktion.csproj \
  --configuration Release \
  --output ./publish

# Create ZIP file (Windows PowerShell)
Compress-Archive -Path ./publish/* -DestinationPath ./deploy.zip -Force

# Or on Linux/Mac
cd publish
zip -r ../deploy.zip .
cd ..

# Deploy to Azure
az webapp deployment source config-zip \
  --name becatracktion-telemetry \
  --resource-group rg-becatracktion \
  --src deploy.zip

# Wait for deployment to complete (30-60 seconds)
```

#### Step 7: Verify Deployment

```bash
# Get the URL
az webapp show \
  --name becatracktion-telemetry \
  --resource-group rg-becatracktion \
  --query defaultHostName \
  --output tsv

# Test the health endpoint
curl https://becatracktion-telemetry.azurewebsites.net/api/telemetry/health

# View logs
az webapp log tail \
  --name becatracktion-telemetry \
  --resource-group rg-becatracktion
```

---

### Method 3: Deploy Using GitHub Actions (CI/CD)

This method automatically deploys when you push to GitHub.

#### Step 1: Create Web App (if not already created)

Follow Method 1 or Method 2 above to create the Web App.

#### Step 2: Get Publish Profile

```bash
# Download publish profile
az webapp deployment list-publishing-profiles \
  --name becatracktion-telemetry \
  --resource-group rg-becatracktion \
  --xml > publish-profile.xml
```

Or in Azure Portal:
1. Go to your App Service
2. Click **"Deployment Center"**
3. Click **"Manage publish profile"**
4. Click **"Download publish profile"**

#### Step 3: Add GitHub Secret

1. Go to your GitHub repository
2. Click **"Settings"** → **"Secrets and variables"** → **"Actions"**
3. Click **"New repository secret"**
4. Name: `AZURE_WEBAPP_PUBLISH_PROFILE`
5. Value: Paste the entire contents of the publish profile XML
6. Click **"Add secret"**

#### Step 4: Create GitHub Actions Workflow

Create `.github/workflows/azure-deploy.yml`:

```yaml
name: Deploy to Azure App Service

on:
  push:
    branches: [ main ]
  workflow_dispatch:

env:
  AZURE_WEBAPP_NAME: becatracktion-telemetry
  DOTNET_VERSION: '8.0.x'

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Restore dependencies
      run: dotnet restore BecaTracktion/BecaTracktion.csproj

    - name: Build
      run: dotnet build BecaTracktion/BecaTracktion.csproj --configuration Release --no-restore

    - name: Publish
      run: dotnet publish BecaTracktion/BecaTracktion.csproj --configuration Release --output ./publish

    - name: Deploy to Azure Web App
      uses: azure/webapps-deploy@v2
      with:
        app-name: ${{ env.AZURE_WEBAPP_NAME }}
        publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE }}
        package: ./publish
```

#### Step 5: Push and Deploy

```bash
git add .github/workflows/azure-deploy.yml
git commit -m "Add Azure deployment workflow"
git push origin main
```

The deployment will start automatically. Check progress:
1. Go to your GitHub repository
2. Click **"Actions"** tab
3. Watch the deployment progress

---

### Method 4: Deploy Using Docker Container

If you prefer containerized deployment.

#### Step 1: Create Azure Container Registry (ACR)

```bash
# Create ACR
az acr create \
  --name becatracktioncr \
  --resource-group rg-becatracktion \
  --sku Basic \
  --location australiaeast

# Login to ACR
az acr login --name becatracktioncr

# Get ACR login server
az acr show \
  --name becatracktioncr \
  --query loginServer \
  --output tsv
# Output: becatracktioncr.azurecr.io
```

#### Step 2: Build and Push Docker Image

```bash
# Build Docker image
docker build -t becatracktion:latest .

# Tag for ACR
docker tag becatracktion:latest becatracktioncr.azurecr.io/becatracktion:latest

# Push to ACR
docker push becatracktioncr.azurecr.io/becatracktion:latest

# Verify
az acr repository list --name becatracktioncr --output table
```

#### Step 3: Create Web App for Containers

```bash
# Create App Service Plan for containers
az appservice plan create \
  --name plan-becatracktion-container \
  --resource-group rg-becatracktion \
  --location australiaeast \
  --is-linux \
  --sku B1

# Create Web App for Containers
az webapp create \
  --name becatracktion-telemetry-container \
  --resource-group rg-becatracktion \
  --plan plan-becatracktion-container \
  --deployment-container-image-name becatracktioncr.azurecr.io/becatracktion:latest

# Configure ACR credentials
az webapp config container set \
  --name becatracktion-telemetry-container \
  --resource-group rg-becatracktion \
  --docker-custom-image-name becatracktioncr.azurecr.io/becatracktion:latest \
  --docker-registry-server-url https://becatracktioncr.azurecr.io \
  --docker-registry-server-user becatracktioncr \
  --docker-registry-server-password $(az acr credential show --name becatracktioncr --query "passwords[0].value" -o tsv)

# Enable continuous deployment
az webapp deployment container config \
  --name becatracktion-telemetry-container \
  --resource-group rg-becatracktion \
  --enable-cd true
```

#### Step 4: Configure Application Settings

```bash
# Set environment variables
az webapp config appsettings set \
  --name becatracktion-telemetry-container \
  --resource-group rg-becatracktion \
  --settings \
    ApplicationInsights__ConnectionString="YOUR_CONNECTION_STRING" \
    ASPNETCORE_ENVIRONMENT="Production"
```

---

### Post-Deployment Configuration

#### Enable HTTPS Only

```bash
# Force HTTPS
az webapp update \
  --name becatracktion-telemetry \
  --resource-group rg-becatracktion \
  --https-only true
```

Or in Azure Portal:
1. Go to your App Service
2. Click **"TLS/SSL settings"**
3. Set **"HTTPS Only"** to **On**

#### Configure Custom Domain (Optional)

```bash
# Add custom domain
az webapp config hostname add \
  --webapp-name becatracktion-telemetry \
  --resource-group rg-becatracktion \
  --hostname telemetry.yourdomain.com

# Bind SSL certificate
az webapp config ssl bind \
  --name becatracktion-telemetry \
  --resource-group rg-becatracktion \
  --certificate-thumbprint <thumbprint> \
  --ssl-type SNI
```

#### Enable Diagnostic Logging

```bash
# Enable application logging
az webapp log config \
  --name becatracktion-telemetry \
  --resource-group rg-becatracktion \
  --application-logging filesystem \
  --level information

# Enable detailed error messages
az webapp log config \
  --name becatracktion-telemetry \
  --resource-group rg-becatracktion \
  --detailed-error-messages true \
  --failed-request-tracing true

# Stream logs
az webapp log tail \
  --name becatracktion-telemetry \
  --resource-group rg-becatracktion
```

#### Configure CORS (if needed)

```bash
# Allow all origins (for testing)
az webapp cors add \
  --name becatracktion-telemetry \
  --resource-group rg-becatracktion \
  --allowed-origins '*'

# Or specific origins (for production)
az webapp cors add \
  --name becatracktion-telemetry \
  --resource-group rg-becatracktion \
  --allowed-origins 'https://yourdomain.com' 'https://app.yourdomain.com'
```

---

### Troubleshooting Azure App Service

#### Issue: App Not Starting (HTTP 500.30)

**Solution:**

1. Check logs:
   ```bash
   az webapp log tail --name becatracktion-telemetry --resource-group rg-becatracktion
   ```

2. Verify runtime:
   ```bash
   az webapp config show --name becatracktion-telemetry --resource-group rg-becatracktion
   ```

3. Check Application Insights connection string is set correctly

4. Ensure `ASPNETCORE_ENVIRONMENT` is set to `Production`

#### Issue: Application Insights Not Working

**Solution:**

1. Verify connection string:
   ```bash
   az webapp config appsettings list \
     --name becatracktion-telemetry \
     --resource-group rg-becatracktion \
     --query "[?name=='ApplicationInsights__ConnectionString']"
   ```

2. Check Application Insights resource exists and is in the same region

3. Restart the app:
   ```bash
   az webapp restart --name becatracktion-telemetry --resource-group rg-becatracktion
   ```

#### Issue: Deployment Failed

**Solution:**

1. Check deployment logs:
   ```bash
   az webapp log deployment show \
     --name becatracktion-telemetry \
     --resource-group rg-becatracktion
   ```

2. Verify publish profile or credentials

3. Try manual ZIP deployment

---

### Monitoring and Cost Optimization

#### View Application Insights

1. Go to Azure Portal
2. Navigate to your Application Insights resource
3. View:
   - **Live Metrics**: Real-time telemetry
   - **Failures**: Exceptions and failed requests
   - **Performance**: Response times and dependencies
   - **Metrics**: Custom metrics from your app

#### Cost Comparison

| Tier | Monthly Cost | Features | Best For |
|------|--------------|----------|----------|
| **Free F1** | $0 | 60 min/day, 1 GB storage | Testing only |
| **Basic B1** | ~$13 | Always on, custom domain, SSL | Small production apps ✅ |
| **Standard S1** | ~$70 | Auto-scale, staging slots, backups | Medium production apps |
| **Premium P1V2** | ~$100 | Enhanced performance, VNet | High-traffic apps |

**Recommendation for BecaTracktion**: Start with **Basic B1** ($13/month)

---

## Kubernetes Deployment (Advanced)

### Step 1: Create Kubernetes Manifests

Create `k8s-deployment.yaml`:

```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: telemetry

---
apiVersion: v1
kind: Secret
metadata:
  name: telemetry-secrets
  namespace: telemetry
type: Opaque
stringData:
  connection-string: "YOUR_CONNECTION_STRING_HERE"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: becatracktion
  namespace: telemetry
spec:
  replicas: 3
  selector:
    matchLabels:
      app: becatracktion
  template:
    metadata:
      labels:
        app: becatracktion
    spec:
      containers:
      - name: becatracktion
        image: becatracktion:latest
        ports:
        - containerPort: 80
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: ApplicationInsights__ConnectionString
          valueFrom:
            secretKeyRef:
              name: telemetry-secrets
              key: connection-string
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/telemetry/health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/telemetry/health
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: becatracktion-service
  namespace: telemetry
spec:
  type: LoadBalancer
  selector:
    app: becatracktion
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
```

### Step 2: Deploy to Kubernetes

```bash
# Apply the manifests
kubectl apply -f k8s-deployment.yaml

# Check deployment status
kubectl get deployments -n telemetry

# Check pods
kubectl get pods -n telemetry

# Check service
kubectl get service -n telemetry

# Get external IP
kubectl get service becatracktion-service -n telemetry
```

### Step 3: Verify Deployment

```bash
# Get the external IP
EXTERNAL_IP=$(kubectl get service becatracktion-service -n telemetry -o jsonpath='{.status.loadBalancer.ingress[0].ip}')

# Test the health endpoint
curl http://$EXTERNAL_IP/api/telemetry/health
```

## Configuration

### Application Insights Connection String

The connection string can be configured in multiple ways (in order of precedence):

1. Environment variable: `ApplicationInsights__ConnectionString`
2. appsettings.json: `ApplicationInsights:ConnectionString`
3. Azure App Configuration
4. Azure Key Vault

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `ASPNETCORE_ENVIRONMENT` | Environment name | Development |
| `ApplicationInsights__ConnectionString` | App Insights connection | (required) |
| `ASPNETCORE_URLS` | URLs to listen on | http://+:80 |

## Monitoring

### Health Checks

The service provides a health check endpoint:

```bash
curl http://your-service-url/api/telemetry/health
```

Expected response:
```json
{
  "status": "Healthy",
  "service": "Telemetry Microservice",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Logging

The service uses ASP.NET Core logging:

- Console logging (development)
- Application Insights logging (production)
- Structured logging with log levels

### Metrics

Monitor these metrics in Application Insights:

- Request rate
- Response time
- Failure rate
- Dependency calls
- Exception count

## Troubleshooting

### Issue: Container won't start

**Solution**:
```bash
# Check container logs
docker logs becatracktion

# Check for port conflicts
netstat -an | grep 8080

# Verify image
docker inspect becatracktion:latest
```

### Issue: Cannot connect to Application Insights

**Solution**:
- Verify connection string is correct
- Check network connectivity
- Verify firewall rules
- Check Application Insights resource status

### Issue: High memory usage

**Solution**:
- Increase container memory limits
- Check for memory leaks in logs
- Monitor Application Insights metrics
- Consider horizontal scaling

### Issue: Slow response times

**Solution**:
- Check Application Insights latency
- Verify network connectivity
- Scale horizontally (add more instances)
- Check resource utilization

## Security Best Practices

1. **Use HTTPS in production**
2. **Store secrets in Azure Key Vault**
3. **Implement authentication**
4. **Restrict CORS origins**
5. **Enable rate limiting**
6. **Use managed identities**
7. **Regular security updates**

## Backup and Recovery

### Configuration Backup

```bash
# Export App Service configuration
az webapp config appsettings list \
  --name becatracktion-telemetry \
  --resource-group rg-becatracktion \
  > config-backup.json
```

### Disaster Recovery

- Use multiple regions for high availability
- Implement health checks and auto-restart
- Regular backups of configuration
- Document recovery procedures

## Support

For issues or questions:
- Check logs first
- Review Application Insights
- Contact development team
- Create support ticket

