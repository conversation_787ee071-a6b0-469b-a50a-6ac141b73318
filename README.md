# BecaTracktion Telemetry Microservice

A lightweight HTTP-based microservice for forwarding telemetry data to Azure Application Insights. This service enables legacy or host-constrained applications (e.g., Revit add-ins) to send telemetry without directly integrating platform-specific SDKs, avoiding dependency conflicts and runtime limitations.

## Architecture

```
┌─────────────────────┐
│  Client Apps        │
│  - Revit Add-ins    │
│  - Python Scripts   │
│  - JavaScript Apps  │
│  - Any HTTP Client  │
└──────────┬──────────┘
           │ HTTP POST
           │ (JSON)
           ▼
┌─────────────────────┐
│  BecaTracktion      │
│  Microservice       │
│  (ASP.NET Core)     │
└──────────┬──────────┘
           │ Application
           │ Insights SDK
           ▼
┌─────────────────────┐
│  Azure Application  │
│  Insights           │
└─────────────────────┘
```

## Features

- **Language-Agnostic**: Any client that can make HTTP requests can send telemetry
- **Event Tracking**: Track custom events with properties and metrics
- **Exception Tracking**: Log exceptions with stack traces and context
- **Metric Tracking**: Send custom metrics for monitoring
- **Page View Tracking**: Track page/view navigation
- **Health Check**: Built-in health check endpoint
- **Swagger UI**: Interactive API documentation
- **Docker Support**: Containerized for easy deployment
- **CORS Enabled**: Cross-origin requests supported

## API Endpoints

### Base URL
- Development: `http://localhost:5004/api/telemetry`
- Production: `https://your-domain.com/api/telemetry`

### Endpoints

#### 1. Track Event
```
POST /api/telemetry/event
```

#### 2. Track Exception
```
POST /api/telemetry/exception
```

#### 3. Track Metric
```
POST /api/telemetry/metric
```

#### 4. Track Page View
```
POST /api/telemetry/pageview
```

#### 5. Flush Telemetry
```
POST /api/telemetry/flush
```

#### 6. Health Check
```
GET /api/telemetry/health
```

## Getting Started

### Prerequisites
- .NET 8.0 SDK
- Azure Application Insights resource (or use the provided connection string)

### Running Locally

1. Clone the repository
2. Navigate to the project directory
3. Restore dependencies:
   ```bash
   dotnet restore
   ```
4. Run the application:
   ```bash
   dotnet run --project BecaTracktion
   ```
5. Open your browser to `http://localhost:5004` to see the Swagger UI

### Configuration

Update the Application Insights connection string in `appsettings.json`:

```json
{
  "ApplicationInsights": {
    "ConnectionString": "YOUR_CONNECTION_STRING_HERE"
  }
}
```

### Docker Deployment

#### Build the Docker image:
```bash
docker build -t becatracktion:latest .
```

#### Run the container:
```bash
docker run -d -p 8080:80 \
  -e ApplicationInsights__ConnectionString="YOUR_CONNECTION_STRING" \
  --name becatracktion \
  becatracktion:latest
```

#### Using Docker Compose:
```yaml
version: '3.8'
services:
  telemetry:
    image: becatracktion:latest
    ports:
      - "8080:80"
    environment:
      - ApplicationInsights__ConnectionString=YOUR_CONNECTION_STRING
    restart: unless-stopped
```

## Client Examples

See the `ClientExamples` directory for detailed examples in:
- C# (.NET)
- Python
- JavaScript (Node.js and Browser)
- PowerShell
- cURL

## Project Structure

```
BecaTracktion/
├── Controllers/
│   └── TelemetryController.cs      # API endpoints
├── Models/
│   ├── TelemetryEvent.cs           # Event model
│   ├── TelemetryException.cs       # Exception model
│   ├── TelemetryMetric.cs          # Metric model
│   ├── TelemetryPageView.cs        # Page view model
│   └── TelemetryResponse.cs        # Response model
├── Services/
│   ├── ITelemetryService.cs        # Service interface
│   └── ApplicationInsightsTelemetryService.cs  # Implementation
├── BaseLogic/
│   ├── BecaBaseCommand.cs          # Reference Revit implementation
│   └── TelemetryHandler.cs         # Reference telemetry handler
├── Program.cs                       # Application entry point
├── appsettings.json                 # Configuration
└── Dockerfile                       # Docker configuration
```

## Benefits

1. **Avoid Dependency Conflicts**: No need to include Application Insights SDK in your host application
2. **Version Independence**: Update telemetry logic without redeploying client applications
3. **Centralized Management**: Single point for telemetry configuration and monitoring
4. **Cross-Platform**: Works with any language or platform that supports HTTP
5. **Scalable**: Can be deployed to Azure App Service, Kubernetes, or any container platform

## Security Considerations

For production deployments, consider:
- Adding API key authentication
- Implementing rate limiting
- Using HTTPS only
- Restricting CORS to specific origins
- Implementing request validation and sanitization

## License

Copyright © Beca. All rights reserved.

## Support

For issues or questions, please contact the development team.

