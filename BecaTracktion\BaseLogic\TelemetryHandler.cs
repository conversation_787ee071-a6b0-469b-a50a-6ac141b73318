﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.ApplicationInsights.Extensibility;
using System;
using System.Collections.Generic;
using System.Security.Principal;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;

namespace BecaTelemetryHandler
{
    public class TelemetryHandler
    {
        #region Static Fields
        static string prjNumber;
        static string rvtVerision;
        static string fileName;
        static DateTime startingTimeForThirdParty;
        #endregion

        #region Fields
        string _addinName;
        string _commandSubName;
        string _eventName;
        DateTime _startingTime;
        TelemetryClient _tc;
        #endregion

        #region Constructor
        private TelemetryHandler(string addinName, string commandSubName, Autodesk.Revit.DB.Document doc)
        {
            _commandSubName = commandSubName;
            _addinName = addinName;
            _eventName = string.IsNullOrEmpty(_commandSubName) ? _addinName : _addinName + "|" + _commandSubName;

            ///Guard code
            if (string.IsNullOrEmpty(rvtVerision))
            {
                SetRvtVersion();
            }
            if (string.IsNullOrEmpty(prjNumber) || string.IsNullOrEmpty(fileName))
            {
                UpdateDocInfo(doc);
            }

            var config = new TelemetryConfiguration
            {
                ConnectionString = "InstrumentationKey=23f5de61-4222-4e87-b594-13fb507f78d6;IngestionEndpoint=https://australiaeast-0.in.applicationinsights.azure.com/;LiveEndpoint=https://australiaeast.livediagnostics.monitor.azure.com/;ApplicationId=e54650bd-f014-4af4-a896-60158d105bf7"
            };
            _tc = new TelemetryClient(config);

            // Set session data:
            _tc.Context.Session.Id = Guid.NewGuid().ToString();
            _tc.Context.Device.OperatingSystem = Environment.OSVersion.ToString();
            _tc.Context.User.Id = doc.Application.Username;
            _tc.TrackPageView(_addinName);

        }
#endregion

        #region Methods
        public static TelemetryHandler CreateTelemetryHandler(string addinName, string commandSubName, Document doc)
        {
            try
            {
                return new TelemetryHandler(addinName, commandSubName, doc);
            }
            catch (Exception ex)
            {
                TaskDialog.Show("Telemetry Warning", "Unable to connect to Microsoft telemetry services. This may be due to a conflict with another add-in that is installed");
                return null;
            }

        }

        public void ProcessingStarter(Document doc)
        {
            _startingTime = DateTime.UtcNow;
            var startDictionary = new Dictionary<string, string>();

            startDictionary.Add("Computer Name", Environment.MachineName);
            startDictionary.Add("Domain Name", Environment.UserDomainName);
            startDictionary.Add("User Name", Environment.UserName);
            try
            {
#if TargetYear2025 || TargetYear2026
                startDictionary.Add("UPN", WindowsIdentity.GetCurrent().Name);
#else
                startDictionary.Add("UPN", System.DirectoryServices.AccountManagement.UserPrincipal.Current.UserPrincipalName);
#endif
            }
            catch (Exception)
            {
                startDictionary.Add("UPN", "Server could not be contacted");
            }


            startDictionary.Add("Organization Name", doc.ProjectInformation?.OrganizationName);
            startDictionary.Add("Building Name", doc.ProjectInformation?.BuildingName);
            startDictionary.Add("Project Number", doc.ProjectInformation.Number);
            startDictionary.Add("Author", doc.ProjectInformation?.Author);
            startDictionary.Add("Client Name", doc.ProjectInformation?.ClientName);

            startDictionary.Add("Model Name", doc.Title);
            startDictionary.Add("Model Path", doc.PathName);
            startDictionary.Add("Revit Version", doc.Application.VersionNumber);
            startDictionary.Add("Version Build", doc.Application.VersionBuild);
            startDictionary.Add("Number of Documents", doc.Application.Documents.Size.ToString());

            _tc.TrackEvent(_eventName + " Started", startDictionary);

        }

        public void ProcessingCompleted()
        {
            TrackEndData("Completed");
        }

        public void ProcessingFailed()
        {
            TrackEndData("Failed");
        }

        public void ProcessingCancelled()
        {
            TrackEndData("Canceled");
        }

        public void TrackEndData(string ProcessStatus)
        {
            var endDictionary = new Dictionary<string, string>();

            endDictionary.Add("Consumed Time", GetConsumedTimeNow(_startingTime));
            endDictionary.Add("Process Status", ProcessStatus);

            _tc.TrackEvent(_eventName + " Ended", endDictionary);

            FlushTelemetryClient();
        }

        public void LogProcessing(Dictionary<string, string> dictionary)
        {
            _tc.TrackEvent(_eventName + " Processing", dictionary);
        }

        public void LogException(Exception ex, string location, IDictionary<string, string> properties = null, IDictionary<string, double> metrics = null)
        {
#if DEBUG
            return;
#endif
            try
            {
                // Prepare event data
                properties = properties ?? new Dictionary<string, string>();
                metrics = metrics ?? new Dictionary<string, double>();

                properties["ExceptionDetails"] = ex.ToString();

                if (!string.IsNullOrEmpty(location))
                {
                    properties.Add("Location", location);
                }

                // Log the exception
                _tc.TrackException(ex, properties, metrics);
            }
            catch (Exception)
            {
                // Eat all exception
            }
        }

        private void FlushTelemetryClient()
        {
            _tc.Flush(); // only for desktop apps

            // Allow time for flushing:
            System.Threading.Thread.Sleep(1000);
        }
#endregion

        #region Static Methods
        public static void SetRvtVersion()
        {
            rvtVerision = Helper.GetCurrentRevitVerision();
        }

        public static void UpdateDocInfo(Autodesk.Revit.DB.Document doc)
        {
            prjNumber = doc.ProjectInformation.Number;
            fileName = doc.Title;
        }

        static string GetConsumedTimeNow(DateTime startingTime)
        {
            var consumedTime = DateTime.UtcNow.Subtract(startingTime);
            return consumedTime.ToString(@"hh\:mm\:ss");
        }
        #endregion
    }
}
