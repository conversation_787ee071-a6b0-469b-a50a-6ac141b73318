Design a telemetry microservice architecture that enables legacy or host-constrained applications (e.g., Revit add-ins) to send telemetry data without directly integrating platform-specific SDKs like Application Insights. The goal is to avoid dependency conflicts and runtime limitations by offloading telemetry logic to an external service.

Requirements:
1. Create a lightweight HTTP-based microservice using ASP.NET Core Web API.
2. The microservice should expose a POST endpoint that accepts telemetry events with custom properties.
3. Internally, the service should use Application Insights SDK to forward telemetry to Azure.
4. Include a Dockerfile to containerize the service for cross-platform deployment.
5. Provide a clean file structure and full code implementation (controllers, models, configuration).
6. Ensure the service is language-agnostic — any client (e.g., .NET, Python, JavaScript) should be able to send telemetry via HTTP.
7. Optional: add support for exception tracking, metrics, or authentication.

Output:
- Architecture diagram showing how host apps interact with the microservice and Application Insights.
- Full codebase with explanations.
- Example client snippets for different platforms (e.g., C#, Python, JavaScript).