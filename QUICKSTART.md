# BecaTracktion Telemetry Microservice - Quick Start Guide

Get up and running with the BecaTracktion Telemetry Microservice in 5 minutes!

## Prerequisites

- .NET 8.0 SDK installed
- (Optional) Docker for containerized deployment

## Step 1: Start the Service (Choose One)

### Option A: Run Locally with .NET

```bash
# Navigate to project directory
cd "BecaTracktion API"

# Run the service
dotnet run --project BecaTracktion
```

The service will start at: http://localhost:5004

### Option B: Run with Docker

```bash
# Build and run with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f
```

The service will start at: http://localhost:8080

## Step 2: Verify the Service

Open your browser to:
- **Swagger UI**: http://localhost:5004 (or http://localhost:8080 for Docker)
- **Health Check**: http://localhost:5004/api/telemetry/health

Or use cURL:

```bash
curl http://localhost:5004/api/telemetry/health
```

Expected response:
```json
{
  "status": "Healthy",
  "service": "Telemetry Microservice",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Step 3: Send Your First Telemetry Event

### Using cURL

```bash
curl -X POST http://localhost:5004/api/telemetry/event \
  -H "Content-Type: application/json" \
  -d '{
    "eventName": "My First Tracktion Event",
    "properties": {
      "source": "quickstart",
      "user": "developer"
    },
    "userId": "your-name"
  }'
```

### Using PowerShell

```powershell
$body = @{
    eventName = "My First Tracktion Event"
    properties = @{
        source = "quickstart"
        user = "developer"
    }
    userId = $env:USERNAME
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:5004/api/telemetry/event" `
    -Method Post `
    -Body $body `
    -ContentType "application/json"
```

### Using Python

```python
import requests

response = requests.post(
    'http://localhost:5004/api/telemetry/event',
    json={
        'eventName': 'My First Event',
        'properties': {
            'source': 'quickstart',
            'user': 'developer'
        },
        'userId': 'your-name'
    }
)

print(response.json())
```

### Using JavaScript (Node.js)

```javascript
const fetch = require('node-fetch');

fetch('http://localhost:5004/api/telemetry/event', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        eventName: 'My First Event',
        properties: {
            source: 'quickstart',
            user: 'developer'
        },
        userId: 'your-name'
    })
})
.then(res => res.json())
.then(data => console.log(data));
```

## Step 4: Explore the API

### Using Swagger UI

1. Open http://localhost:5004 in your browser
2. You'll see all available endpoints
3. Click "Try it out" on any endpoint
4. Fill in the request body
5. Click "Execute"
6. See the response

### Available Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/telemetry/event` | POST | Track custom events |
| `/api/telemetry/exception` | POST | Track exceptions |
| `/api/telemetry/metric` | POST | Track metrics |
| `/api/telemetry/pageview` | POST | Track page views |
| `/api/telemetry/flush` | POST | Force flush telemetry |
| `/api/telemetry/health` | GET | Health check |

## Step 5: Integrate with Your Application

### For C# / .NET Applications

1. Copy `ClientExamples/CSharp/TelemetryClient.cs` to your project

2. Use it in your code:

```csharp
using BecaTelemetryClient;

var telemetry = new TelemetryClient("http://localhost:5004");

// Track an event
await telemetry.TrackEventAsync(
    "Application Started",
    new Dictionary<string, string> 
    {
        { "version", "1.0.0" },
        { "environment", "production" }
    }
);

// Track an exception
try
{
    // Your code
}
catch (Exception ex)
{
    await telemetry.TrackExceptionAsync(ex, "MyMethod");
}

// Ensure telemetry is sent
await telemetry.FlushAsync();
```

### For Python Applications

1. Copy `ClientExamples/Python/telemetry_client.py` to your project

2. Use it in your code:

```python
from telemetry_client import TelemetryClient

telemetry = TelemetryClient('http://localhost:5004')

# Track an event
telemetry.track_event(
    'Application Started',
    properties={
        'version': '1.0.0',
        'environment': 'production'
    }
)

# Track an exception
try:
    # Your code
    pass
except Exception as ex:
    telemetry.track_exception(ex, 'my_function')

# Ensure telemetry is sent
telemetry.flush()
```

### For JavaScript Applications

1. Copy `ClientExamples/JavaScript/telemetry-client.js` to your project

2. Use it in your code:

```javascript
const TelemetryClient = require('./telemetry-client');

const telemetry = new TelemetryClient('http://localhost:5004');

// Track an event
await telemetry.trackEvent(
    'Application Started',
    {
        version: '1.0.0',
        environment: 'production'
    }
);

// Track an exception
try {
    // Your code
} catch (error) {
    await telemetry.trackException(error, 'myFunction');
}

// Ensure telemetry is sent
await telemetry.flush();
```

## Step 6: View Telemetry in Azure

1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to your Application Insights resource
3. Go to **Logs**
4. Run this query:

```kusto
customEvents
| where timestamp > ago(1h)
| project timestamp, name, customDimensions
| order by timestamp desc
```

You should see your telemetry events!

## Common Use Cases

### Revit Add-in Integration

```csharp
// In your Revit command
public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
{
    var telemetry = new TelemetryClient("http://your-service-url");
    var doc = commandData.Application.ActiveUIDocument.Document;
    
    // Track command start
    await telemetry.TrackEventAsync(
        "Revit Command Started",
        new Dictionary<string, string>
        {
            { "Command", "MyCommand" },
            { "Revit Version", doc.Application.VersionNumber },
            { "Project", doc.ProjectInformation.Number }
        }
    );
    
    try
    {
        // Your command logic
        
        await telemetry.TrackEventAsync("Revit Command Completed");
        return Result.Succeeded;
    }
    catch (Exception ex)
    {
        await telemetry.TrackExceptionAsync(ex, "Execute");
        return Result.Failed;
    }
    finally
    {
        await telemetry.FlushAsync();
    }
}
```

### Python Script Monitoring

```python
from telemetry_client import TelemetryClient
import time

telemetry = TelemetryClient('http://your-service-url')

# Track script execution
start_time = time.time()

telemetry.track_event('Script Started', {'script': 'data_processor.py'})

try:
    # Your script logic
    process_data()
    
    duration = time.time() - start_time
    telemetry.track_metric('Processing Time', duration)
    telemetry.track_event('Script Completed', {'status': 'success'})
    
except Exception as ex:
    telemetry.track_exception(ex, 'process_data')
    telemetry.track_event('Script Failed', {'error': str(ex)})
    
finally:
    telemetry.flush()
```

## Troubleshooting

### Service won't start

**Check if port is in use:**
```bash
# Windows
netstat -ano | findstr :5004

# Linux/Mac
lsof -i :5004
```

**Solution:** Change the port in `Properties/launchSettings.json`

### Can't connect to Application Insights

**Check connection string:**
- Verify in `appsettings.json`
- Ensure it's a valid Application Insights connection string
- Test connectivity to Azure

### Telemetry not appearing in Azure

**Wait a few minutes** - There can be a delay of 1-5 minutes

**Force flush:**
```bash
curl -X POST http://localhost:5004/api/telemetry/flush
```

**Check Application Insights status** in Azure Portal

## Next Steps

- 📖 Read the [Architecture Documentation](ARCHITECTURE.md)
- 🚀 Follow the [Deployment Guide](DEPLOYMENT.md)
- 🧪 Run the [Testing Guide](TESTING.md)
- 📝 Review [Client Examples](ClientExamples/)

## Need Help?

- Check the [README.md](README.md) for detailed information
- Review the [Implementation Summary](IMPLEMENTATION_SUMMARY.md)
- Contact the development team

## Quick Reference

### Service URLs
- Local: http://localhost:5004
- Docker: http://localhost:8080
- Swagger: http://localhost:5004/

### Key Commands

```bash
# Start service
dotnet run --project BecaTracktion

# Start with Docker
docker-compose up -d

# Stop Docker
docker-compose down

# View logs
docker-compose logs -f

# Test health
curl http://localhost:5004/api/telemetry/health
```

---

**Congratulations!** 🎉 You now have a working telemetry microservice that can collect telemetry from any application without SDK dependencies!

