# BecaTracktion Telemetry Client for PowerShell
# Can be used in PowerShell scripts or automation

function Send-TelemetryEvent {
    <#
    .SYNOPSIS
        Send a telemetry event to BecaTracktion microservice
    
    .PARAMETER BaseUrl
        Base URL of the telemetry service
    
    .PARAMETER EventName
        Name of the event to track
    
    .PARAMETER Properties
        Hashtable of custom properties
    
    .PARAMETER Metrics
        Hashtable of custom metrics
    
    .PARAMETER UserId
        Optional user identifier (defaults to current user)
    
    .PARAMETER SessionId
        Optional session identifier (defaults to new GUID)
    
    .EXAMPLE
        Send-TelemetryEvent -BaseUrl "http://localhost:5004" -EventName "Script Started" -Properties @{ScriptName="Deploy.ps1"}
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true)]
        [string]$BaseUrl,
        
        [Parameter(Mandatory=$true)]
        [string]$EventName,
        
        [Parameter(Mandatory=$false)]
        [hashtable]$Properties = @{},
        
        [Parameter(Mandatory=$false)]
        [hashtable]$Metrics = $null,
        
        [Parameter(Mandatory=$false)]
        [string]$UserId = $env:USERNAME,
        
        [Parameter(Mandatory=$false)]
        [string]$SessionId = [guid]::NewGuid().ToString()
    )
    
    try {
        $body = @{
            eventName = $EventName
            properties = $Properties
            metrics = $Metrics
            userId = $UserId
            sessionId = $SessionId
            timestamp = (Get-Date).ToUniversalTime().ToString("o")
        } | ConvertTo-Json -Depth 10
        
        $uri = "$($BaseUrl.TrimEnd('/'))/api/telemetry/event"
        
        $response = Invoke-RestMethod -Uri $uri -Method Post -Body $body -ContentType "application/json" -TimeoutSec 10
        
        return $response.success
    }
    catch {
        Write-Warning "Error tracking event: $_"
        return $false
    }
}

function Send-TelemetryException {
    <#
    .SYNOPSIS
        Send an exception to BecaTracktion microservice
    
    .PARAMETER BaseUrl
        Base URL of the telemetry service
    
    .PARAMETER Exception
        The exception to track
    
    .PARAMETER Location
        Optional location where the exception occurred
    
    .PARAMETER Properties
        Hashtable of custom properties
    
    .PARAMETER UserId
        Optional user identifier (defaults to current user)
    
    .PARAMETER SessionId
        Optional session identifier (defaults to new GUID)
    
    .EXAMPLE
        try { ... } catch { Send-TelemetryException -BaseUrl "http://localhost:5004" -Exception $_ -Location "Deploy Script" }
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true)]
        [string]$BaseUrl,
        
        [Parameter(Mandatory=$true)]
        $Exception,
        
        [Parameter(Mandatory=$false)]
        [string]$Location = $null,
        
        [Parameter(Mandatory=$false)]
        [hashtable]$Properties = @{},
        
        [Parameter(Mandatory=$false)]
        [string]$UserId = $env:USERNAME,
        
        [Parameter(Mandatory=$false)]
        [string]$SessionId = [guid]::NewGuid().ToString()
    )
    
    try {
        $body = @{
            message = $Exception.Exception.Message
            exceptionType = $Exception.Exception.GetType().FullName
            stackTrace = $Exception.ScriptStackTrace
            location = $Location
            properties = $Properties
            userId = $UserId
            sessionId = $SessionId
            timestamp = (Get-Date).ToUniversalTime().ToString("o")
        } | ConvertTo-Json -Depth 10
        
        $uri = "$($BaseUrl.TrimEnd('/'))/api/telemetry/exception"
        
        $response = Invoke-RestMethod -Uri $uri -Method Post -Body $body -ContentType "application/json" -TimeoutSec 10
        
        return $response.success
    }
    catch {
        Write-Warning "Error tracking exception: $_"
        return $false
    }
}

function Send-TelemetryMetric {
    <#
    .SYNOPSIS
        Send a metric to BecaTracktion microservice
    
    .PARAMETER BaseUrl
        Base URL of the telemetry service
    
    .PARAMETER MetricName
        Name of the metric
    
    .PARAMETER Value
        Numeric value of the metric
    
    .PARAMETER Properties
        Hashtable of custom properties
    
    .PARAMETER UserId
        Optional user identifier (defaults to current user)
    
    .PARAMETER SessionId
        Optional session identifier (defaults to new GUID)
    
    .EXAMPLE
        Send-TelemetryMetric -BaseUrl "http://localhost:5004" -MetricName "Deployment Duration" -Value 45.2
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true)]
        [string]$BaseUrl,
        
        [Parameter(Mandatory=$true)]
        [string]$MetricName,
        
        [Parameter(Mandatory=$true)]
        [double]$Value,
        
        [Parameter(Mandatory=$false)]
        [hashtable]$Properties = $null,
        
        [Parameter(Mandatory=$false)]
        [string]$UserId = $env:USERNAME,
        
        [Parameter(Mandatory=$false)]
        [string]$SessionId = [guid]::NewGuid().ToString()
    )
    
    try {
        $body = @{
            metricName = $MetricName
            value = $Value
            properties = $Properties
            userId = $UserId
            sessionId = $SessionId
            timestamp = (Get-Date).ToUniversalTime().ToString("o")
        } | ConvertTo-Json -Depth 10
        
        $uri = "$($BaseUrl.TrimEnd('/'))/api/telemetry/metric"
        
        $response = Invoke-RestMethod -Uri $uri -Method Post -Body $body -ContentType "application/json" -TimeoutSec 10
        
        return $response.success
    }
    catch {
        Write-Warning "Error tracking metric: $_"
        return $false
    }
}

function Send-TelemetryFlush {
    <#
    .SYNOPSIS
        Flush telemetry to ensure it's sent
    
    .PARAMETER BaseUrl
        Base URL of the telemetry service
    
    .EXAMPLE
        Send-TelemetryFlush -BaseUrl "http://localhost:5004"
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true)]
        [string]$BaseUrl
    )
    
    try {
        $uri = "$($BaseUrl.TrimEnd('/'))/api/telemetry/flush"
        
        $response = Invoke-RestMethod -Uri $uri -Method Post -TimeoutSec 10
        
        return $response.success
    }
    catch {
        Write-Warning "Error flushing telemetry: $_"
        return $false
    }
}

# Example usage
function Example-Usage {
    $baseUrl = "http://localhost:5004"
    $sessionId = [guid]::NewGuid().ToString()
    
    # Track script start
    Send-TelemetryEvent -BaseUrl $baseUrl -EventName "PowerShell Script Started" `
        -Properties @{
            ScriptName = "Deploy.ps1"
            PowerShellVersion = $PSVersionTable.PSVersion.ToString()
            ComputerName = $env:COMPUTERNAME
        } -SessionId $sessionId
    
    try {
        $startTime = Get-Date
        
        # Your script logic here
        Start-Sleep -Seconds 1
        
        # Track metric
        $duration = ((Get-Date) - $startTime).TotalSeconds
        Send-TelemetryMetric -BaseUrl $baseUrl -MetricName "Script Duration" -Value $duration -SessionId $sessionId
        
        # Track success
        Send-TelemetryEvent -BaseUrl $baseUrl -EventName "PowerShell Script Completed" `
            -Properties @{
                Status = "Success"
                Duration = $duration.ToString("F2")
            } -SessionId $sessionId
    }
    catch {
        # Track exception
        Send-TelemetryException -BaseUrl $baseUrl -Exception $_ -Location "Example-Usage" `
            -Properties @{ScriptName = "Deploy.ps1"} -SessionId $sessionId
        
        Send-TelemetryEvent -BaseUrl $baseUrl -EventName "PowerShell Script Failed" `
            -Properties @{Error = $_.Exception.Message} -SessionId $sessionId
    }
    finally {
        # Ensure telemetry is sent
        Send-TelemetryFlush -BaseUrl $baseUrl
    }
}

# Uncomment to run example
# Example-Usage

