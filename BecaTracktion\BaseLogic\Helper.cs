﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BecaTelemetryHandler
{
    internal class Helper
    {
        public static string GetCurrentRevitVerision()
        {
            #region Setting revit verision
#if TargetYear2021
            return "2021";
#elif <PERSON><PERSON>ear2022
            return "2022";
#elif <PERSON>Year2023
            return "2023";
#elif TargetYear2024
            return "2024";
#elif TargetYear2025
            return "2025";
#elif TargetYear2026
            return "2026";
#else
            return "Unknown"; // Should never happen, check why TargetYear2026 not defined (Configuration Manager)
#endif
            #endregion
        }
    }
}
