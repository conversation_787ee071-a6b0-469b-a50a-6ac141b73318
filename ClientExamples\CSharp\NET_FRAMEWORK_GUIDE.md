# .NET Framework 4.8 Integration Guide

This guide shows how to use the telemetry client with **older Revit versions** (2023 and below) that target .NET Framework 4.8.

---

## 🎯 Which Client to Use?

| Revit Version | .NET Target | Client File | JSON Library |
|---------------|-------------|-------------|--------------|
| **2024-2026+** | .NET 8 | `TelemetryClientSync.cs` | System.Text.Json |
| **2023 and below** | .NET Framework 4.8 | `TelemetryClientSyncNet48.cs` ✅ | Newtonsoft.Json |

---

## 🚀 Quick Start for .NET Framework 4.8

### Step 1: Add NuGet Package

Your Revit add-in project needs **Newtonsoft.Json**:

```xml
<!-- In your .csproj file -->
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
```

Or via Package Manager Console:
```powershell
Install-Package Newtonsoft.Json -Version 13.0.3
```

### Step 2: Copy the Client File

Copy `TelemetryClientSyncNet48.cs` to your Revit add-in project.

### Step 3: Basic Usage

```csharp
using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaTelemetryClient;
using System;
using System.Collections.Generic;

namespace MyRevitAddin
{
    [Transaction(TransactionMode.Manual)]
    public class MyCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            // Use the .NET Framework 4.8 compatible client
            using (var telemetry = new TelemetryClientSyncNet48("https://tracktion.azurewebsites.net"))
            {
                UIApplication uiapp = commandData.Application;
                UIDocument uidoc = uiapp.ActiveUIDocument;
                Document doc = uidoc.Document;

                // Track command start
                telemetry.TrackEvent(
                    "My Command Started",
                    new Dictionary<string, string>
                    {
                        { "Revit Version", uiapp.Application.VersionNumber },
                        { "Project Number", doc.ProjectInformation.Number },
                        { "Model Name", doc.Title },
                        { "User Name", Environment.UserName },
                        { "Computer Name", Environment.MachineName }
                    });

                try
                {
                    // Your command logic here
                    DoSomething(doc);

                    // Track success
                    telemetry.TrackEvent("My Command Completed", 
                        new Dictionary<string, string> { { "Status", "Success" } });

                    return Result.Succeeded;
                }
                catch (Exception ex)
                {
                    // Track exception
                    telemetry.TrackException(ex, "MyCommand.Execute");

                    telemetry.TrackEvent("My Command Failed",
                        new Dictionary<string, string> { { "Error", ex.Message } });

                    message = ex.Message;
                    return Result.Failed;
                }
                finally
                {
                    // Ensure telemetry is sent before command ends
                    telemetry.Flush();
                }
            }
        }

        private void DoSomething(Document doc)
        {
            // Your Revit logic here
        }
    }
}
```

---

## 🔧 Key Differences from .NET 8 Version

### JSON Serialization
- **.NET 8**: Uses `System.Text.Json`
- **.NET Framework 4.8**: Uses `Newtonsoft.Json` ✅

### String Interpolation
- **.NET 8**: Uses `$"{variable}"` syntax
- **.NET Framework 4.8**: Uses `string.Format()` or concatenation ✅

### HttpClient
- Both versions use the same `HttpClient` approach
- Both use `.Result` for synchronous calls

---

## 📦 Required NuGet Packages

For .NET Framework 4.8 projects, you need:

```xml
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
<PackageReference Include="System.Net.Http" Version="4.3.4" />
```

**Note**: `System.Net.Http` is usually already included in .NET Framework 4.8 projects.

---

## 🏗️ Integration with BecaBaseCommand

### Option 1: Use the Helper Class

```csharp
using BecaTelemetryClient;

// In your BecaBaseCommand.Execute method:
var telemetryHelper = new BecaBaseCommandWithTelemetryNet48();
telemetryHelper.InitializeTelemetry(addinName, commandSubName, "https://tracktion.azurewebsites.net");

// Track start
telemetryHelper.ProcessingStarter(new Dictionary<string, string>
{
    { "Revit Version", doc.Application.VersionNumber },
    { "Project Number", doc.ProjectInformation.Number }
});

try
{
    // Your command logic
    var result = ExecuteBecaCommand(commandData, ref message, elements);
    
    if (result == Result.Succeeded)
        telemetryHelper.ProcessingCompleted();
    else if (result == Result.Failed)
        telemetryHelper.ProcessingFailed();
    else
        telemetryHelper.ProcessingCancelled();
        
    return result;
}
catch (Exception ex)
{
    telemetryHelper.LogException(ex, "ExecuteBecaCommand");
    telemetryHelper.ProcessingFailed();
    throw;
}
finally
{
    telemetryHelper.Dispose();
}
```

### Option 2: Direct Integration

```csharp
using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaTelemetryClient;
using System;
using System.Collections.Generic;

namespace BecaCommand
{
    [Transaction(TransactionMode.Manual)]
    public abstract class BecaBaseCommand : IExternalCommand
    {
        protected TelemetryClientSyncNet48 telemetry;
        private DateTime startTime;

        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            UIApplication uiapp = commandData.Application;
            UIDocument uidoc = uiapp.ActiveUIDocument;
            Document doc = uidoc.Document;

            var addinName = GetAddinName();
            var commandSubName = GetCommandSubName();

            // Initialize telemetry (.NET Framework 4.8 version)
            try
            {
                telemetry = new TelemetryClientSyncNet48("https://tracktion.azurewebsites.net");
                ProcessingStarter(doc, addinName, commandSubName);
            }
            catch (Exception ex)
            {
                // Telemetry failed, but don't break the command
                TaskDialog.Show("Telemetry Warning", 
                    "Unable to connect to telemetry service. Command will continue.");
            }

            Result becaCommandResult;
            try
            {
                becaCommandResult = ExecuteBecaCommand(commandData, ref message, elements);
            }
            catch (Exception ex)
            {
                becaCommandResult = Result.Failed;
                if (telemetry != null)
                {
                    telemetry.TrackException(ex, addinName + ".ExecuteBecaCommand");
                }
            }

            // Track completion
            switch (becaCommandResult)
            {
                case Result.Failed:
                    ProcessingFailed(addinName, commandSubName);
                    break;
                case Result.Succeeded:
                    ProcessingCompleted(addinName, commandSubName);
                    break;
                case Result.Cancelled:
                    ProcessingCancelled(addinName, commandSubName);
                    break;
            }

            if (telemetry != null)
            {
                telemetry.Dispose();
            }
            return becaCommandResult;
        }

        private void ProcessingStarter(Document doc, string addinName, string commandSubName)
        {
            if (telemetry == null) return;

            startTime = DateTime.UtcNow;
            var eventName = string.IsNullOrEmpty(commandSubName) 
                ? addinName + " Started" 
                : addinName + "|" + commandSubName + " Started";

            var properties = new Dictionary<string, string>
            {
                { "Computer Name", Environment.MachineName },
                { "User Name", Environment.UserName },
                { "Project Number", doc.ProjectInformation.Number },
                { "Model Name", doc.Title },
                { "Revit Version", doc.Application.VersionNumber }
            };

            telemetry.TrackEvent(eventName, properties);
        }

        private void ProcessingCompleted(string addinName, string commandSubName)
        {
            if (telemetry == null) return;

            var eventName = string.IsNullOrEmpty(commandSubName)
                ? addinName + " Ended"
                : addinName + "|" + commandSubName + " Ended";

            var duration = (DateTime.UtcNow - startTime).TotalSeconds;
            var properties = new Dictionary<string, string>
            {
                { "Consumed Time", TimeSpan.FromSeconds(duration).ToString(@"hh\:mm\:ss") },
                { "Process Status", "Completed" }
            };

            telemetry.TrackEvent(eventName, properties);
            telemetry.Flush();
        }

        private void ProcessingFailed(string addinName, string commandSubName)
        {
            if (telemetry == null) return;

            var eventName = string.IsNullOrEmpty(commandSubName)
                ? addinName + " Ended"
                : addinName + "|" + commandSubName + " Ended";

            var duration = (DateTime.UtcNow - startTime).TotalSeconds;
            var properties = new Dictionary<string, string>
            {
                { "Consumed Time", TimeSpan.FromSeconds(duration).ToString(@"hh\:mm\:ss") },
                { "Process Status", "Failed" }
            };

            telemetry.TrackEvent(eventName, properties);
            telemetry.Flush();
        }

        private void ProcessingCancelled(string addinName, string commandSubName)
        {
            if (telemetry == null) return;

            var eventName = string.IsNullOrEmpty(commandSubName)
                ? addinName + " Ended"
                : addinName + "|" + commandSubName + " Ended";

            var duration = (DateTime.UtcNow - startTime).TotalSeconds;
            var properties = new Dictionary<string, string>
            {
                { "Consumed Time", TimeSpan.FromSeconds(duration).ToString(@"hh\:mm\:ss") },
                { "Process Status", "Canceled" }
            };

            telemetry.TrackEvent(eventName, properties);
            telemetry.Flush();
        }

        public abstract Result ExecuteBecaCommand(ExternalCommandData commandData, ref string message, ElementSet elements);
        public abstract string GetAddinName();
        public abstract string GetCommandSubName();
        public abstract string GetAddinAuthor();
    }
}
```

---

## 🔍 Testing

### Test with Revit 2023

1. Copy `TelemetryClientSyncNet48.cs` to your Revit 2023 add-in project
2. Add Newtonsoft.Json NuGet package
3. Update your service URL to: `https://tracktion.azurewebsites.net`
4. Test with a simple command
5. Check Application Insights for telemetry data

### Verify Compatibility

```csharp
// Simple test method
public void TestTelemetry()
{
    using (var telemetry = new TelemetryClientSyncNet48("https://tracktion.azurewebsites.net"))
    {
        var success = telemetry.TrackEvent("Test Event", new Dictionary<string, string>
        {
            { "Test", "Value" },
            { "Revit Version", "2023" }
        });

        if (success)
        {
            TaskDialog.Show("Success", "Telemetry sent successfully!");
        }
        else
        {
            TaskDialog.Show("Error", "Failed to send telemetry.");
        }

        telemetry.Flush();
    }
}
```

---

## 🚨 Troubleshooting

### Issue: "Could not load Newtonsoft.Json"

**Solution**: Make sure you have the correct version:
```xml
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
```

### Issue: "System.Net.Http not found"

**Solution**: Add explicit reference:
```xml
<PackageReference Include="System.Net.Http" Version="4.3.4" />
```

### Issue: "String interpolation not supported"

**Solution**: The .NET Framework 4.8 version uses string concatenation instead of interpolation. This is already handled in the provided code.

---

## 📋 Summary

### For Revit 2024-2026+ (.NET 8)
- Use: `TelemetryClientSync.cs`
- JSON: `System.Text.Json`
- Features: Modern C# syntax

### For Revit 2023 and below (.NET Framework 4.8)
- Use: `TelemetryClientSyncNet48.cs` ✅
- JSON: `Newtonsoft.Json`
- Features: Compatible with older .NET Framework

Both clients have the **same API** and **same functionality** - just different underlying implementations for compatibility.

---

## 🎯 Next Steps

1. **Identify your Revit versions**
2. **Choose the appropriate client**:
   - Revit 2024+ → `TelemetryClientSync.cs`
   - Revit 2023- → `TelemetryClientSyncNet48.cs`
3. **Add required NuGet packages**
4. **Update your service URL** to `https://tracktion.azurewebsites.net`
5. **Test with a simple command**
6. **Deploy to all your add-ins**

You're all set! 🚀
