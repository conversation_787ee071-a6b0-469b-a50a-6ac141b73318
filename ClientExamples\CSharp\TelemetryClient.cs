using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace BecaTelemetryClient
{
    /// <summary>
    /// Client for sending telemetry to BecaTracktion microservice
    /// This can be used in Revit add-ins or any .NET application
    /// </summary>
    public class TelemetryClient : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private readonly string _userId;
        private readonly string _sessionId;

        public TelemetryClient(string baseUrl, string userId = null)
        {
            _baseUrl = baseUrl.TrimEnd('/');
            _httpClient = new HttpClient
            {
                Timeout = TimeSpan.FromSeconds(10)
            };
            _userId = userId ?? Environment.UserName;
            _sessionId = Guid.NewGuid().ToString();
        }

        /// <summary>
        /// Track a custom event
        /// </summary>
        public async Task<bool> TrackEventAsync(
            string eventName,
            Dictionary<string, string> properties = null,
            Dictionary<string, double> metrics = null)
        {
            try
            {
                var telemetryEvent = new
                {
                    EventName = eventName,
                    Properties = properties ?? new Dictionary<string, string>(),
                    Metrics = metrics,
                    UserId = _userId,
                    SessionId = _sessionId,
                    Timestamp = DateTime.UtcNow
                };

                var response = await _httpClient.PostAsJsonAsync(
                    $"{_baseUrl}/api/telemetry/event",
                    telemetryEvent);

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error tracking event: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Track an exception
        /// </summary>
        public async Task<bool> TrackExceptionAsync(
            Exception exception,
            string location = null,
            Dictionary<string, string> properties = null)
        {
            try
            {
                var telemetryException = new
                {
                    Message = exception.Message,
                    ExceptionType = exception.GetType().FullName,
                    StackTrace = exception.StackTrace,
                    Location = location,
                    Properties = properties ?? new Dictionary<string, string>(),
                    UserId = _userId,
                    SessionId = _sessionId,
                    Timestamp = DateTime.UtcNow
                };

                var response = await _httpClient.PostAsJsonAsync(
                    $"{_baseUrl}/api/telemetry/exception",
                    telemetryException);

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error tracking exception: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Track a metric
        /// </summary>
        public async Task<bool> TrackMetricAsync(
            string metricName,
            double value,
            Dictionary<string, string> properties = null)
        {
            try
            {
                var telemetryMetric = new
                {
                    MetricName = metricName,
                    Value = value,
                    Properties = properties,
                    UserId = _userId,
                    SessionId = _sessionId,
                    Timestamp = DateTime.UtcNow
                };

                var response = await _httpClient.PostAsJsonAsync(
                    $"{_baseUrl}/api/telemetry/metric",
                    telemetryMetric);

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error tracking metric: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Track a page view
        /// </summary>
        public async Task<bool> TrackPageViewAsync(
            string pageName,
            Dictionary<string, string> properties = null)
        {
            try
            {
                var telemetryPageView = new
                {
                    PageName = pageName,
                    Properties = properties,
                    UserId = _userId,
                    SessionId = _sessionId,
                    Timestamp = DateTime.UtcNow
                };

                var response = await _httpClient.PostAsJsonAsync(
                    $"{_baseUrl}/api/telemetry/pageview",
                    telemetryPageView);

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error tracking page view: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Flush telemetry to ensure it's sent
        /// </summary>
        public async Task<bool> FlushAsync()
        {
            try
            {
                var response = await _httpClient.PostAsync(
                    $"{_baseUrl}/api/telemetry/flush",
                    null);

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error flushing telemetry: {ex.Message}");
                return false;
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    /// <summary>
    /// Example usage in a Revit add-in
    /// </summary>
    public class RevitCommandExample
    {
        public async Task ExecuteCommandAsync()
        {
            using (var telemetry = new TelemetryClient("http://localhost:5004"))
            {
                var startTime = DateTime.UtcNow;

                // Track command start
                await telemetry.TrackEventAsync(
                    "Revit Command Started",
                    new Dictionary<string, string>
                    {
                        { "Command", "MyRevitCommand" },
                        { "Revit Version", "2024" },
                        { "Computer Name", Environment.MachineName }
                    });

                try
                {
                    // Your command logic here
                    await Task.Delay(1000); // Simulate work

                    // Track success
                    var duration = (DateTime.UtcNow - startTime).TotalSeconds;
                    await telemetry.TrackMetricAsync("Command Duration", duration);

                    await telemetry.TrackEventAsync(
                        "Revit Command Completed",
                        new Dictionary<string, string>
                        {
                            { "Status", "Success" },
                            { "Duration", duration.ToString("F2") }
                        });
                }
                catch (Exception ex)
                {
                    // Track exception
                    await telemetry.TrackExceptionAsync(
                        ex,
                        "RevitCommandExample.ExecuteCommandAsync",
                        new Dictionary<string, string>
                        {
                            { "Command", "MyRevitCommand" }
                        });

                    await telemetry.TrackEventAsync(
                        "Revit Command Failed",
                        new Dictionary<string, string>
                        {
                            { "Error", ex.Message }
                        });
                }
                finally
                {
                    // Ensure telemetry is sent
                    await telemetry.FlushAsync();
                }
            }
        }
    }
}

