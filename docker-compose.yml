version: '3.8'

services:
  telemetry:
    build:
      context: .
      dockerfile: Dockerfile
    image: becatracktion:latest
    container_name: becatracktion-telemetry
    ports:
      - "8080:80"
      - "8443:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      # Override the Application Insights connection string
      - ApplicationInsights__ConnectionString=${APP_INSIGHTS_CONNECTION_STRING:-InstrumentationKey=23f5de61-4222-4e87-b594-13fb507f78d6;IngestionEndpoint=https://australiaeast-0.in.applicationinsights.azure.com/;LiveEndpoint=https://australiaeast.livediagnostics.monitor.azure.com/;ApplicationId=e54650bd-f014-4af4-a896-60158d105bf7}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/api/telemetry/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - telemetry-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  telemetry-network:
    driver: bridge

