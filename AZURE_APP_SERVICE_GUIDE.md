# Azure App Service Deployment Guide for BecaTracktion

## Quick Reference

This guide provides step-by-step instructions for deploying BecaTracktion Telemetry Microservice to Azure App Service using the latest 2024/2025 methods.

---

## Table of Contents

1. [Understanding Azure Options](#understanding-azure-options)
2. [Prerequisites](#prerequisites)
3. [Method 1: Azure Portal (Visual)](#method-1-azure-portal-visual)
4. [Method 2: Azure CLI (Command Line)](#method-2-azure-cli-command-line)
5. [Method 3: Visual Studio](#method-3-visual-studio)
6. [Method 4: GitHub Actions (CI/CD)](#method-4-github-actions-cicd)
7. [Post-Deployment](#post-deployment)
8. [Troubleshooting](#troubleshooting)

---

## Understanding Azure Options

When you click "Create" in Azure Portal, you'll see these options:

| Option | Use Case | Choose for BecaTracktion? |
|--------|----------|---------------------------|
| **Web App** | ASP.NET Core APIs, Web Apps | ✅ **YES - Choose this!** |
| Static Web App | Static HTML/JS, React, Angular | ❌ No - for static sites only |
| Web App + Database | Apps with SQL Database | ❌ No - we don't need SQL |
| WordPress on App Service | WordPress sites | ❌ No - for WordPress only |

**Answer: Choose "Web App"** for BecaTracktion!

---

## Prerequisites

- Azure account (free tier available with $200 credit)
- Application Insights connection string
- One of the following:
  - Azure Portal access (for Method 1)
  - Azure CLI installed (for Method 2)
  - Visual Studio 2022 (for Method 3)
  - GitHub account (for Method 4)

---

## Method 1: Azure Portal (Visual)

**Best for:** First-time deployment, visual learners

### Step-by-Step Instructions

#### 1. Sign in to Azure Portal

- Go to: https://portal.azure.com
- Sign in with your Microsoft account

#### 2. Create Web App

1. Type "App Services" in the search bar → Click "App Services"
2. Click "+ Create" → Select **"Web App"**

#### 3. Fill in Basic Settings

| Setting | Value | Notes |
|---------|-------|-------|
| **Subscription** | Your subscription | Select from dropdown |
| **Resource Group** | `rg-becatracktion` | Click "Create new" |
| **Name** | `becatracktion-telemetry` | Must be globally unique |
| **Publish** | **Code** | NOT Container |
| **Runtime stack** | **.NET 8 (LTS)** | From dropdown |
| **Operating System** | **Windows** | Recommended |
| **Region** | **Australia East** | Or closest to you |

#### 4. Configure App Service Plan

| Setting | Value | Cost |
|---------|-------|------|
| **Plan Name** | `plan-becatracktion` | - |
| **Pricing Tier** | **Basic B1** | ~$13/month ✅ |

**Pricing Options:**
- Free F1: $0 (limited, testing only)
- Basic B1: $13/month (recommended for production)
- Standard S1: $70/month (auto-scaling)

#### 5. Review and Create

1. Click "Review + create"
2. Verify all settings
3. Click "Create"
4. Wait 1-2 minutes
5. Click "Go to resource"

#### 6. Configure Application Settings

1. In your App Service, click **"Configuration"** (left menu)
2. Click **"+ New application setting"**
3. Add:
   - **Name**: `ApplicationInsights__ConnectionString`
   - **Value**: Your connection string (from Application Insights)
4. Click "OK" → Click "Save" → Click "Continue"

#### 7. Deploy Your Code

**Option A: Upload ZIP file**

1. Publish locally:
   ```powershell
   cd "c:\Users\<USER>\Documents\Folders\TRACKTION\BecaTracktion API"
   dotnet publish BecaTracktion/BecaTracktion.csproj -c Release -o ./publish
   ```

2. Create ZIP of the `publish` folder

3. In Azure Portal:
   - Go to your App Service
   - Click "Deployment Center"
   - Click "FTPS credentials" tab
   - Use FTP client to upload ZIP

**Option B: Use Azure CLI** (see Method 2)

**Option C: Use Visual Studio** (see Method 3)

#### 8. Verify Deployment

1. In App Service, click "Overview"
2. Click the URL (e.g., `https://becatracktion-telemetry.azurewebsites.net`)
3. You should see Swagger UI
4. Test: `https://becatracktion-telemetry.azurewebsites.net/api/telemetry/health`

---

## Method 2: Azure CLI (Command Line)

**Best for:** Automation, scripting, developers

### Prerequisites

- Install Azure CLI: https://aka.ms/installazurecliwindows

### Complete Script

```powershell
# 1. Login to Azure
az login

# 2. Create Resource Group
az group create `
  --name rg-becatracktion `
  --location australiaeast

# 3. Create App Service Plan (Basic B1)
az appservice plan create `
  --name plan-becatracktion `
  --resource-group rg-becatracktion `
  --location australiaeast `
  --sku B1

# 4. Create Web App
az webapp create `
  --name becatracktion-telemetry `
  --resource-group rg-becatracktion `
  --plan plan-becatracktion `
  --runtime "DOTNET|8"

# 5. Configure Application Settings
az webapp config appsettings set `
  --name becatracktion-telemetry `
  --resource-group rg-becatracktion `
  --settings ApplicationInsights__ConnectionString="YOUR_CONNECTION_STRING_HERE"

# 6. Publish Application
cd "c:\Users\<USER>\Documents\Folders\TRACKTION\BecaTracktion API"
dotnet publish BecaTracktion/BecaTracktion.csproj -c Release -o ./publish

# 7. Create ZIP and Deploy
Compress-Archive -Path ./publish/* -DestinationPath ./deploy.zip -Force
az webapp deployment source config-zip `
  --name becatracktion-telemetry `
  --resource-group rg-becatracktion `
  --src deploy.zip

# 8. Get URL
az webapp show `
  --name becatracktion-telemetry `
  --resource-group rg-becatracktion `
  --query defaultHostName `
  --output tsv

# 9. Test
$url = az webapp show --name becatracktion-telemetry --resource-group rg-becatracktion --query defaultHostName -o tsv
curl "https://$url/api/telemetry/health"
```

### Copy-Paste Ready Commands

```powershell
# All-in-one deployment script
az login
az group create --name rg-becatracktion --location australiaeast
az appservice plan create --name plan-becatracktion --resource-group rg-becatracktion --location australiaeast --sku B1
az webapp create --name becatracktion-telemetry --resource-group rg-becatracktion --plan plan-becatracktion --runtime "DOTNET|8"
az webapp config appsettings set --name becatracktion-telemetry --resource-group rg-becatracktion --settings ApplicationInsights__ConnectionString="YOUR_CONNECTION_STRING"
cd "c:\Users\<USER>\Documents\Folders\TRACKTION\BecaTracktion API"
dotnet publish BecaTracktion/BecaTracktion.csproj -c Release -o ./publish
Compress-Archive -Path ./publish/* -DestinationPath ./deploy.zip -Force
az webapp deployment source config-zip --name becatracktion-telemetry --resource-group rg-becatracktion --src deploy.zip
```

---

## Method 3: Visual Studio

**Best for:** .NET developers using Visual Studio

### Steps

1. Open BecaTracktion solution in Visual Studio 2022
2. Right-click `BecaTracktion` project → **"Publish..."**
3. Click **"Azure"** → **"Next"**
4. Select **"Azure App Service (Windows)"** → **"Next"**
5. Sign in to Azure
6. Select your subscription
7. Click **"+ Create new"**
8. Fill in:
   - **Name**: `becatracktion-telemetry`
   - **Resource Group**: Create new `rg-becatracktion`
   - **Hosting Plan**: Create new `plan-becatracktion` (Basic B1)
9. Click **"Create"**
10. Click **"Finish"**
11. Click **"Publish"**

### Configure Application Insights

After publishing:
1. Go to Azure Portal
2. Navigate to your App Service
3. Click "Configuration"
4. Add Application Insights connection string (see Method 1, Step 6)

---

## Method 4: GitHub Actions (CI/CD)

**Best for:** Continuous deployment, team collaboration

### Steps

1. **Get Publish Profile**
   - Go to Azure Portal → Your App Service
   - Click "Deployment Center"
   - Click "Manage publish profile"
   - Click "Download publish profile"

2. **Add GitHub Secret**
   - Go to your GitHub repository
   - Settings → Secrets and variables → Actions
   - Click "New repository secret"
   - Name: `AZURE_WEBAPP_PUBLISH_PROFILE`
   - Value: Paste entire publish profile XML
   - Click "Add secret"

3. **Create Workflow File**

Create `.github/workflows/azure-deploy.yml`:

```yaml
name: Deploy to Azure App Service

on:
  push:
    branches: [ main ]
  workflow_dispatch:

env:
  AZURE_WEBAPP_NAME: becatracktion-telemetry
  DOTNET_VERSION: '8.0.x'

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}
    
    - name: Restore
      run: dotnet restore BecaTracktion/BecaTracktion.csproj
    
    - name: Build
      run: dotnet build BecaTracktion/BecaTracktion.csproj --configuration Release --no-restore
    
    - name: Publish
      run: dotnet publish BecaTracktion/BecaTracktion.csproj --configuration Release --output ./publish
    
    - name: Deploy to Azure
      uses: azure/webapps-deploy@v2
      with:
        app-name: ${{ env.AZURE_WEBAPP_NAME }}
        publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE }}
        package: ./publish
```

4. **Push to GitHub**
   ```bash
   git add .github/workflows/azure-deploy.yml
   git commit -m "Add Azure deployment workflow"
   git push origin main
   ```

5. **Monitor Deployment**
   - Go to GitHub repository → Actions tab
   - Watch the deployment progress

---

## Post-Deployment

### Enable HTTPS Only

```powershell
az webapp update `
  --name becatracktion-telemetry `
  --resource-group rg-becatracktion `
  --https-only true
```

### Enable Always On (prevents cold starts)

```powershell
az webapp config set `
  --name becatracktion-telemetry `
  --resource-group rg-becatracktion `
  --always-on true
```

### View Logs

```powershell
az webapp log tail `
  --name becatracktion-telemetry `
  --resource-group rg-becatracktion
```

---

## Troubleshooting

### Issue: "Name already taken"

**Solution:** Change the app name to something unique:
- Try: `becatracktion-telemetry-yourname`
- Or: `becatracktion-telemetry-2025`

### Issue: HTTP 500.30 error

**Solution:**
1. Check logs: `az webapp log tail --name becatracktion-telemetry --resource-group rg-becatracktion`
2. Verify Application Insights connection string is set
3. Restart app: `az webapp restart --name becatracktion-telemetry --resource-group rg-becatracktion`

### Issue: Can't find .NET 8 runtime

**Solution:** Make sure you selected **.NET 8 (LTS)** not .NET 6 or 7

### Issue: Deployment takes too long

**Solution:** This is normal for first deployment (2-5 minutes). Subsequent deployments are faster.

---

## Quick Commands Reference

```powershell
# View app URL
az webapp show --name becatracktion-telemetry --resource-group rg-becatracktion --query defaultHostName -o tsv

# Restart app
az webapp restart --name becatracktion-telemetry --resource-group rg-becatracktion

# View logs
az webapp log tail --name becatracktion-telemetry --resource-group rg-becatracktion

# Stop app
az webapp stop --name becatracktion-telemetry --resource-group rg-becatracktion

# Start app
az webapp start --name becatracktion-telemetry --resource-group rg-becatracktion

# Delete everything
az group delete --name rg-becatracktion --yes
```

---

## Next Steps

1. ✅ Deploy to Azure App Service
2. ✅ Test with Swagger UI
3. ✅ Configure Application Insights
4. 📝 Update Revit add-ins to use the new URL
5. 📝 Test telemetry from Revit
6. 📝 Monitor in Application Insights

---

## Support

- **Azure Documentation**: https://docs.microsoft.com/azure/app-service/
- **Pricing Calculator**: https://azure.microsoft.com/pricing/calculator/
- **Azure Support**: https://azure.microsoft.com/support/

---

**Estimated Monthly Cost**: ~$13 USD (Basic B1 tier)

**Deployment Time**: 5-10 minutes (first time)

