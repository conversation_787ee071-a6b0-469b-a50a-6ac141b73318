# BecaTracktion Telemetry Microservice - Documentation Index

Welcome to the BecaTracktion Telemetry Microservice documentation! This index will help you find the information you need.

## 🚀 Getting Started

**New to the project? Start here:**

1. **[QUICKSTART.md](QUICKSTART.md)** - Get up and running in 5 minutes
   - Prerequisites
   - Starting the service
   - Sending your first telemetry
   - Basic integration examples

2. **[README.md](README.md)** - Project overview and features
   - Architecture diagram
   - Features list
   - API endpoints
   - Benefits

## 📚 Core Documentation

### Understanding the System

- **[PROJECT_OVERVIEW.md](PROJECT_OVERVIEW.md)** - Comprehensive project overview
  - Executive summary
  - Problem statement and solution
  - Project structure
  - Key components
  - Technical stack
  - Success metrics

- **[ARCHITECTURE.md](ARCHITECTURE.md)** - Detailed architecture documentation
  - Architecture diagrams
  - Component details
  - Data flow
  - Deployment options
  - Security considerations
  - Scalability discussion

- **[IMPLEMENTATION_SUMMARY.md](IMPLEMENTATION_SUMMARY.md)** - Implementation details
  - What was created
  - How it solves the problem
  - Comparison: before vs after
  - Usage examples
  - Benefits achieved

### Deployment and Operations

- **[DEPLOYMENT.md](DEPLOYMENT.md)** - Complete deployment guide
  - Local development setup
  - Docker deployment
  - Azure App Service deployment
  - Kubernetes deployment
  - Configuration management
  - Monitoring setup
  - Troubleshooting

- **[AZURE_APP_SERVICE_GUIDE.md](AZURE_APP_SERVICE_GUIDE.md)** - ⭐ **NEW!** Detailed Azure deployment (2024/2025)
  - Understanding Azure options (Web App vs Static Web App vs others)
  - Method 1: Azure Portal (step-by-step with screenshots descriptions)
  - Method 2: Azure CLI (automation scripts)
  - Method 3: Visual Studio (right-click publish)
  - Method 4: GitHub Actions (CI/CD)
  - Post-deployment configuration
  - Troubleshooting common issues
  - Cost optimization tips

### Testing

- **[TESTING.md](TESTING.md)** - Comprehensive testing guide
  - Manual testing with Swagger and cURL
  - Unit testing examples
  - Integration testing
  - Load testing
  - Client testing
  - CI/CD integration

## 💻 Client Integration

### Client Libraries

All client examples are in the `ClientExamples/` directory:

#### C# / .NET
- **[ClientExamples/CSharp/TelemetryClient.cs](ClientExamples/CSharp/TelemetryClient.cs)**
  - Full-featured C# client (async)
  - Async/await support
  - Example usage for general .NET apps
  - Strongly-typed methods

- **[ClientExamples/CSharp/TelemetryClientSync.cs](ClientExamples/CSharp/TelemetryClientSync.cs)** - ⭐ **NEW!**
  - Synchronous C# client for Revit 2024+ (.NET 8)
  - No async/await (Revit-compatible)
  - BecaBaseCommand integration helper
  - Drop-in replacement for TelemetryHandler

- **[ClientExamples/CSharp/TelemetryClientSyncNet48.cs](ClientExamples/CSharp/TelemetryClientSyncNet48.cs)** - ⭐ **NEW!**
  - Synchronous C# client for Revit 2023 and below (.NET Framework 4.8)
  - Uses Newtonsoft.Json (compatible with older Revit)
  - Same API as .NET 8 version
  - BecaBaseCommand integration helper

- **[ClientExamples/CSharp/REVIT_INTEGRATION_GUIDE.md](ClientExamples/CSharp/REVIT_INTEGRATION_GUIDE.md)** - ⭐ **NEW!**
  - Complete Revit integration guide (Revit 2024+)
  - Why synchronous is needed
  - Step-by-step integration
  - BecaBaseCommand examples
  - Best practices and troubleshooting

- **[ClientExamples/CSharp/NET_FRAMEWORK_GUIDE.md](ClientExamples/CSharp/NET_FRAMEWORK_GUIDE.md)** - ⭐ **NEW!**
  - Complete guide for Revit 2023 and below
  - .NET Framework 4.8 compatibility
  - Newtonsoft.Json setup
  - Same functionality as .NET 8 version

#### Python
- **[ClientExamples/Python/telemetry_client.py](ClientExamples/Python/telemetry_client.py)**
  - Python 3.x compatible
  - Type hints included
  - Works with Dynamo scripts
  - Example usage included

#### JavaScript
- **[ClientExamples/JavaScript/telemetry-client.js](ClientExamples/JavaScript/telemetry-client.js)**
  - Works in Node.js and browser
  - Promise-based API
  - Example usage for both environments
  - Error handling

#### PowerShell
- **[ClientExamples/PowerShell/Send-Telemetry.ps1](ClientExamples/PowerShell/Send-Telemetry.ps1)**
  - Cmdlet-style functions
  - Pipeline support
  - Example usage
  - Automation-ready

#### cURL / Command Line
- **[ClientExamples/cURL/examples.sh](ClientExamples/cURL/examples.sh)**
  - Complete cURL examples
  - All endpoint examples
  - Revit workflow example
  - Ready to run

## 🏗️ Code Structure

### Application Code

```
BecaTracktion/
├── Controllers/
│   └── TelemetryController.cs          # REST API endpoints
├── Models/
│   ├── TelemetryEvent.cs               # Event model
│   ├── TelemetryException.cs           # Exception model
│   ├── TelemetryMetric.cs              # Metric model
│   ├── TelemetryPageView.cs            # Page view model
│   └── TelemetryResponse.cs            # Response model
├── Services/
│   ├── ITelemetryService.cs            # Service interface
│   └── ApplicationInsightsTelemetryService.cs  # Implementation
├── BaseLogic/                          # Reference implementation
│   ├── BecaBaseCommand.cs              # Original Revit code
│   └── TelemetryHandler.cs             # Original telemetry handler
├── Program.cs                          # Application startup
└── appsettings.json                    # Configuration
```

### Reference Implementation

The service is based on working code from a Revit add-in:

- **[BecaTracktion/BaseLogic/BecaBaseCommand.cs](BecaTracktion/BaseLogic/BecaBaseCommand.cs)**
  - Original Revit command base class
  - Shows the problem (SDK conflicts)
  - Reference for integration patterns

- **[BecaTracktion/BaseLogic/TelemetryHandler.cs](BecaTracktion/BaseLogic/TelemetryHandler.cs)**
  - Original telemetry handler
  - Working Application Insights integration
  - Basis for the microservice implementation

## 🔧 Configuration Files

### Application Configuration
- **[BecaTracktion/appsettings.json](BecaTracktion/appsettings.json)** - Production configuration
- **[BecaTracktion/appsettings.Development.json](BecaTracktion/appsettings.Development.json)** - Development configuration
- **[BecaTracktion/BecaTracktion.csproj](BecaTracktion/BecaTracktion.csproj)** - Project file with dependencies

### Deployment Configuration
- **[Dockerfile](Dockerfile)** - Docker container configuration
- **[.dockerignore](.dockerignore)** - Docker ignore file
- **[docker-compose.yml](docker-compose.yml)** - Docker Compose configuration

## 📖 Quick Reference

### Common Tasks

| Task | Documentation | Quick Command |
|------|---------------|---------------|
| Start service locally | [QUICKSTART.md](QUICKSTART.md) | `dotnet run --project BecaTracktion` |
| Start with Docker | [DEPLOYMENT.md](DEPLOYMENT.md) | `docker-compose up -d` |
| Test health endpoint | [TESTING.md](TESTING.md) | `curl http://localhost:5004/api/telemetry/health` |
| View Swagger UI | [QUICKSTART.md](QUICKSTART.md) | Open http://localhost:5004 |
| Deploy to Azure | [DEPLOYMENT.md](DEPLOYMENT.md) | See Azure App Service section |
| Run tests | [TESTING.md](TESTING.md) | `dotnet test` |

### API Endpoints

| Endpoint | Method | Purpose | Documentation |
|----------|--------|---------|---------------|
| `/api/telemetry/event` | POST | Track events | [README.md](README.md) |
| `/api/telemetry/exception` | POST | Track exceptions | [README.md](README.md) |
| `/api/telemetry/metric` | POST | Track metrics | [README.md](README.md) |
| `/api/telemetry/pageview` | POST | Track page views | [README.md](README.md) |
| `/api/telemetry/flush` | POST | Flush telemetry | [README.md](README.md) |
| `/api/telemetry/health` | GET | Health check | [README.md](README.md) |

## 🎯 Use Case Guides

### For Revit Add-in Developers
1. Read [IMPLEMENTATION_SUMMARY.md](IMPLEMENTATION_SUMMARY.md) - "Usage Example: Revit Add-in Integration"
2. Copy [ClientExamples/CSharp/TelemetryClient.cs](ClientExamples/CSharp/TelemetryClient.cs)
3. Replace direct Application Insights calls with HTTP calls
4. Test with different Revit versions

### For Python Developers
1. Read [QUICKSTART.md](QUICKSTART.md) - "Using Python"
2. Copy [ClientExamples/Python/telemetry_client.py](ClientExamples/Python/telemetry_client.py)
3. Import and use in your scripts
4. Works with Dynamo and standalone scripts

### For JavaScript Developers
1. Read [QUICKSTART.md](QUICKSTART.md) - "Using JavaScript"
2. Copy [ClientExamples/JavaScript/telemetry-client.js](ClientExamples/JavaScript/telemetry-client.js)
3. Use in Node.js or browser
4. Promise-based API

### For DevOps Engineers
1. Read [DEPLOYMENT.md](DEPLOYMENT.md)
2. Choose deployment option (Docker, Azure, Kubernetes)
3. Configure Application Insights
4. Set up monitoring and alerts

## 🔍 Troubleshooting

### Common Issues

| Issue | Solution | Documentation |
|-------|----------|---------------|
| Service won't start | Check port availability | [QUICKSTART.md](QUICKSTART.md) - Troubleshooting |
| Can't connect to App Insights | Verify connection string | [DEPLOYMENT.md](DEPLOYMENT.md) - Configuration |
| Telemetry not appearing | Wait 1-5 minutes, force flush | [TESTING.md](TESTING.md) - Verification |
| Docker container issues | Check logs | [DEPLOYMENT.md](DEPLOYMENT.md) - Troubleshooting |

## 📊 Architecture Diagrams

Visual representations of the system:

1. **High-Level Architecture** - See [README.md](README.md)
   - Client apps → Microservice → Application Insights

2. **Detailed Component Diagram** - See [ARCHITECTURE.md](ARCHITECTURE.md)
   - API Layer, Service Layer, SDK Integration

3. **Data Flow Diagram** - See [ARCHITECTURE.md](ARCHITECTURE.md)
   - Request flow from client to Azure

4. **Deployment Diagram** - See [DEPLOYMENT.md](DEPLOYMENT.md)
   - Various deployment options

## 🎓 Learning Path

### Beginner
1. ✅ [QUICKSTART.md](QUICKSTART.md) - Get it running
2. ✅ [README.md](README.md) - Understand what it does
3. ✅ Try Swagger UI - Explore the API
4. ✅ Send test telemetry - See it work

### Intermediate
1. ✅ [ARCHITECTURE.md](ARCHITECTURE.md) - Understand the design
2. ✅ [Client Examples](ClientExamples/) - Integrate with your app
3. ✅ [TESTING.md](TESTING.md) - Test thoroughly
4. ✅ [DEPLOYMENT.md](DEPLOYMENT.md) - Deploy to staging

### Advanced
1. ✅ [IMPLEMENTATION_SUMMARY.md](IMPLEMENTATION_SUMMARY.md) - Deep dive
2. ✅ Review source code - Understand implementation
3. ✅ Customize for your needs - Extend functionality
4. ✅ Production deployment - Go live

## 🔗 External Resources

### Azure Documentation
- [Application Insights Overview](https://docs.microsoft.com/azure/azure-monitor/app/app-insights-overview)
- [Application Insights SDK](https://docs.microsoft.com/azure/azure-monitor/app/asp-net-core)
- [Azure App Service](https://docs.microsoft.com/azure/app-service/)

### .NET Documentation
- [ASP.NET Core Web API](https://docs.microsoft.com/aspnet/core/web-api/)
- [Dependency Injection](https://docs.microsoft.com/aspnet/core/fundamentals/dependency-injection)
- [Configuration](https://docs.microsoft.com/aspnet/core/fundamentals/configuration/)

### Docker Documentation
- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose](https://docs.docker.com/compose/)
- [Dockerfile Best Practices](https://docs.docker.com/develop/develop-images/dockerfile_best-practices/)

## 📝 Document Versions

| Document | Last Updated | Version |
|----------|--------------|---------|
| INDEX.md | 2024-01-15 | 1.0.0 |
| README.md | 2024-01-15 | 1.0.0 |
| QUICKSTART.md | 2024-01-15 | 1.0.0 |
| ARCHITECTURE.md | 2024-01-15 | 1.0.0 |
| DEPLOYMENT.md | 2024-01-15 | 1.0.0 |
| TESTING.md | 2024-01-15 | 1.0.0 |
| IMPLEMENTATION_SUMMARY.md | 2024-01-15 | 1.0.0 |
| PROJECT_OVERVIEW.md | 2024-01-15 | 1.0.0 |

## 🤝 Contributing

### For Internal Team
1. Review the codebase
2. Test with your applications
3. Provide feedback
4. Report issues
5. Suggest improvements

### Documentation Updates
- Keep documentation in sync with code
- Update version numbers
- Add new examples as needed
- Improve clarity based on feedback

## 📞 Support

### Getting Help
1. Check this index for relevant documentation
2. Review the specific guide for your task
3. Check troubleshooting sections
4. Contact the development team

### Reporting Issues
- Provide detailed description
- Include error messages and logs
- Specify environment (local, Docker, Azure)
- Include steps to reproduce

---

## 🎉 Quick Links

**Most Popular:**
- 🚀 [Quick Start](QUICKSTART.md) - Get started in 5 minutes
- 📖 [README](README.md) - Project overview
- 🏗️ [Architecture](ARCHITECTURE.md) - System design
- 🚢 [Deployment](DEPLOYMENT.md) - Deploy the service

**For Developers:**
- 💻 [C# Client](ClientExamples/CSharp/TelemetryClient.cs)
- 🐍 [Python Client](ClientExamples/Python/telemetry_client.py)
- 📜 [JavaScript Client](ClientExamples/JavaScript/telemetry-client.js)
- ⚡ [PowerShell](ClientExamples/PowerShell/Send-Telemetry.ps1)

**Reference:**
- 📊 [Project Overview](PROJECT_OVERVIEW.md)
- 📝 [Implementation Summary](IMPLEMENTATION_SUMMARY.md)
- 🧪 [Testing Guide](TESTING.md)

---

**Need help?** Start with [QUICKSTART.md](QUICKSTART.md) or contact the development team.

**Ready to deploy?** See [DEPLOYMENT.md](DEPLOYMENT.md) for detailed instructions.

**Want to understand the architecture?** Read [ARCHITECTURE.md](ARCHITECTURE.md).

