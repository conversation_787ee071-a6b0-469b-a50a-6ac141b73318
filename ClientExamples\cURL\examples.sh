#!/bin/bash
# BecaTracktion Telemetry cURL Examples
# These examples can be used from any command line or adapted for other tools

BASE_URL="http://localhost:5004"

echo "=== BecaTracktion Telemetry API Examples ==="
echo ""

# 1. Health Check
echo "1. Health Check"
curl -X GET "${BASE_URL}/api/telemetry/health" \
  -H "Content-Type: application/json"
echo -e "\n"

# 2. Track Event
echo "2. Track Event"
curl -X POST "${BASE_URL}/api/telemetry/event" \
  -H "Content-Type: application/json" \
  -d '{
    "eventName": "Application Started",
    "properties": {
      "application": "MyApp",
      "version": "1.0.0",
      "environment": "production"
    },
    "metrics": {
      "startupTime": 2.5
    },
    "userId": "<EMAIL>",
    "sessionId": "550e8400-e29b-41d4-a716-446655440000",
    "timestamp": "2024-01-15T10:30:00Z"
  }'
echo -e "\n"

# 3. Track Exception
echo "3. Track Exception"
curl -X POST "${BASE_URL}/api/telemetry/exception" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Null reference exception occurred",
    "exceptionType": "System.NullReferenceException",
    "stackTrace": "at MyApp.ProcessData() in Program.cs:line 42",
    "location": "MyApp.ProcessData",
    "properties": {
      "module": "DataProcessor",
      "operation": "ProcessData"
    },
    "userId": "<EMAIL>",
    "sessionId": "550e8400-e29b-41d4-a716-446655440000",
    "timestamp": "2024-01-15T10:31:00Z"
  }'
echo -e "\n"

# 4. Track Metric
echo "4. Track Metric"
curl -X POST "${BASE_URL}/api/telemetry/metric" \
  -H "Content-Type: application/json" \
  -d '{
    "metricName": "Processing Time",
    "value": 125.5,
    "properties": {
      "unit": "milliseconds",
      "operation": "DataProcessing"
    },
    "userId": "<EMAIL>",
    "sessionId": "550e8400-e29b-41d4-a716-446655440000",
    "timestamp": "2024-01-15T10:32:00Z"
  }'
echo -e "\n"

# 5. Track Page View
echo "5. Track Page View"
curl -X POST "${BASE_URL}/api/telemetry/pageview" \
  -H "Content-Type: application/json" \
  -d '{
    "pageName": "Dashboard",
    "properties": {
      "url": "/dashboard",
      "referrer": "/home",
      "browser": "Chrome"
    },
    "userId": "<EMAIL>",
    "sessionId": "550e8400-e29b-41d4-a716-446655440000",
    "timestamp": "2024-01-15T10:33:00Z"
  }'
echo -e "\n"

# 6. Flush Telemetry
echo "6. Flush Telemetry"
curl -X POST "${BASE_URL}/api/telemetry/flush" \
  -H "Content-Type: application/json"
echo -e "\n"

# Example: Revit Add-in Workflow
echo "=== Example: Revit Add-in Workflow ==="
echo ""

# Start event
echo "Tracking: Revit Command Started"
curl -X POST "${BASE_URL}/api/telemetry/event" \
  -H "Content-Type: application/json" \
  -d '{
    "eventName": "Revit Command Started",
    "properties": {
      "commandName": "BecaWallTool",
      "revitVersion": "2024",
      "computerName": "WORKSTATION-01",
      "userName": "john.doe",
      "projectNumber": "PRJ-2024-001",
      "modelName": "Building_A.rvt"
    },
    "userId": "<EMAIL>",
    "sessionId": "revit-session-123",
    "timestamp": "2024-01-15T10:35:00Z"
  }'
echo -e "\n"

# Processing metric
echo "Tracking: Processing Metric"
curl -X POST "${BASE_URL}/api/telemetry/metric" \
  -H "Content-Type: application/json" \
  -d '{
    "metricName": "Walls Created",
    "value": 25,
    "properties": {
      "commandName": "BecaWallTool"
    },
    "userId": "<EMAIL>",
    "sessionId": "revit-session-123",
    "timestamp": "2024-01-15T10:36:00Z"
  }'
echo -e "\n"

# Completion event
echo "Tracking: Revit Command Completed"
curl -X POST "${BASE_URL}/api/telemetry/event" \
  -H "Content-Type: application/json" \
  -d '{
    "eventName": "Revit Command Completed",
    "properties": {
      "commandName": "BecaWallTool",
      "status": "Success",
      "duration": "00:01:23"
    },
    "metrics": {
      "durationSeconds": 83
    },
    "userId": "<EMAIL>",
    "sessionId": "revit-session-123",
    "timestamp": "2024-01-15T10:36:23Z"
  }'
echo -e "\n"

# Flush to ensure data is sent
echo "Flushing telemetry"
curl -X POST "${BASE_URL}/api/telemetry/flush" \
  -H "Content-Type: application/json"
echo -e "\n"

echo "=== Examples Complete ==="

